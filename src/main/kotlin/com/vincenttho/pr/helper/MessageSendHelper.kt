package com.vincenttho.pr.helper

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.vincenttho.pr.enums.SysConfigEnum
import com.vincenttho.pr.repository.SchedulerSysConfigRepository
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.web.client.RestClientException

/**
 * 消息发送帮助类
 * <AUTHOR>
 * @date 2025-05-09
 */
@Service
class MessageSendHelper(
    private val httpHelper: HttpHelper,
    private val schedulerSysConfigRepository: SchedulerSysConfigRepository
) {
    private val logger = LoggerFactory.getLogger(MessageSendHelper::class.java)
    private val objectMapper = ObjectMapper()

    // 重试配置
    private val retryDelayMs = 1000L // 重试间隔，单位毫秒

    /**
     * 发送消息，失败时自动重试
     *
     * @param message 要发送的消息内容
     * @param url 可选的URL，默认为空字符串
     * @return 服务器响应
     * @throws RestClientException 如果所有重试都失败
     */
    fun sendMessage(message: String, wxApiKey: String): String {

        // TODO 解密key

        var lastException: Exception? = null
        var lastExceptionMessage: String? = null

        val maxRetries = schedulerSysConfigRepository.findValue(SysConfigEnum.API_RETRY_TIMES)!!.toInt()
        val url = schedulerSysConfigRepository.findValue(SysConfigEnum.WX_MESSAGE_URL)!!
        val completeUrl = "${url}?key=${wxApiKey}"

        val json = """
            {
            	"msgtype": "text",
            	"text": {
            		"content": "${message}"
            	}
            }
        """.trimIndent()

        for (attempt in 1..maxRetries) {
            try {
                // 调用HTTP工具类发送POST请求
                logger.info("开始调用微信企业号接口推送消息...")
                logger.info("调用微信企业号接口推送消息，入参：${json}")
                val response = httpHelper.post(completeUrl, json)
                logger.info("调用微信企业号接口推送消息结束，出参：${response}")

                if(isFailed(response)) {
                    lastExceptionMessage = getErrorMessage(response)
                    logger.warn("发送消息失败 (尝试 $attempt/$maxRetries): ${lastExceptionMessage}")
                    if (attempt < maxRetries) {
                        logger.info("${retryDelayMs}毫秒后重试...")
                        Thread.sleep(retryDelayMs)
                        continue
                    }
                    break
                }

                // 如果发送成功，直接返回结果
                return response
            } catch (e: Exception) {
                lastException = e
                lastExceptionMessage = e.message
                logger.warn("发送消息失败 (尝试 $attempt/$maxRetries): ${e.message}")

                // 如果还有重试机会，等待一段时间后重试
                if (attempt < maxRetries) {
                    logger.info("${retryDelayMs}毫秒后重试...")
                    Thread.sleep(retryDelayMs)
                }
            }
        }

        // 如果所有重试都失败，抛出最后一个异常
        throw lastException ?: RestClientException("发送消息失败，已重试 $maxRetries 次，调用地址：${completeUrl}，发送消息：${message}\n，错误信息：" + (lastExceptionMessage?:""))
    }

    /**
     * 判断消息发送是否失败
     *
     * @param response 服务器返回的JSON响应
     * @return 如果发送失败返回true，否则返回false
     */
    fun isFailed(response: String): Boolean {
        try {
            // 使用Jackson解析JSON
            val jsonNode = objectMapper.readTree(response)

            // 检查errcode字段，如果为0则表示成功，否则表示失败
            val errCode = jsonNode.get("errcode")?.asInt() ?: -1
            return errCode != 0
        } catch (e: Exception) {
            logger.error("解析响应JSON失败: ${e.message}")
            // 如果解析失败，认为请求失败
            return true
        }
    }

    /**
     * 获取错误消息
     * @param response 服务器返回的JSON响应
     * @return 错误消息，如果没有错误或解析失败则返回空字符串
     */
    fun getErrorMessage(response: String): String {
        try {
            // 使用Jackson解析JSON
            val jsonNode = objectMapper.readTree(response)

            // 获取errcode和errmsg
            val errCode = jsonNode.get("errcode")?.asInt() ?: -1

            // 如果errcode不为0，返回错误消息
            if (errCode != 0) {
                val errMsg = jsonNode.get("errmsg")?.asText() ?: ""
                return "错误码: $errCode, 错误信息: $errMsg"
            }
            return ""
        } catch (e: Exception) {
            logger.error("解析响应JSON失败: ${e.message}")
            return "解析响应失败: ${e.message}"
        }
    }
}