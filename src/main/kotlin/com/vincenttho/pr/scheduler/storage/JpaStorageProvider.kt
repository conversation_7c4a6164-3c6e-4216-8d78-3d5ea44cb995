package com.vincenttho.pr.scheduler.storage

import com.vincenttho.pr.scheduler.entity.SchedulerConfig
import com.vincenttho.pr.scheduler.entity.SchedulerExecution
import com.vincenttho.pr.scheduler.repository.SchedulerConfigRepository
import com.vincenttho.pr.scheduler.repository.SchedulerExecutionRepository
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import java.time.LocalDateTime

/**
 * JPA-based storage provider for schedulers
 * <AUTHOR>
 */
class JpaStorageProvider(
    private val schedulerConfigRepository: SchedulerConfigRepository,
    private val schedulerExecutionRepository: SchedulerExecutionRepository
) : SchedulerStorageProvider {

    /**
     * Get the scheduler execution repository
     * @return SchedulerExecutionRepository
     */
    fun getSchedulerExecutionRepository(): SchedulerExecutionRepository {
        return schedulerExecutionRepository
    }

    override fun findAllSchedulers(): List<SchedulerConfig> {
        return schedulerConfigRepository.findAll()
    }

    override fun findSchedulerById(id: String): SchedulerConfig? {
        return schedulerConfigRepository.findById(id).orElse(null)
    }

    override fun saveScheduler(config: SchedulerConfig): SchedulerConfig {
        // Update the updatedAt field
        val updatedConfig = config.copy(updatedAt = LocalDateTime.now())
        return schedulerConfigRepository.save(updatedConfig)
    }

    override fun deleteScheduler(id: String) {
        schedulerConfigRepository.deleteById(id)
    }

    override fun schedulerExists(id: String): Boolean {
        return schedulerConfigRepository.existsById(id)
    }

    override fun saveExecution(execution: SchedulerExecution): SchedulerExecution {
        return schedulerExecutionRepository.save(execution)
    }

    override fun findExecutionsBySchedulerId(schedulerId: String, pageable: Pageable): Page<SchedulerExecution> {
        // Use the custom implementation for legacy Oracle pagination
        return schedulerExecutionRepository.findBySchedulerIdOrderByStartTimeDescWithLegacyPagination(schedulerId, pageable)
    }

    override fun updateNextQueryStartTime(schedulerConfig: SchedulerConfig, nextQueryStartTime: LocalDateTime) {
        schedulerConfigRepository.updateNextQueryStartTime(schedulerConfig.id, nextQueryStartTime)
    }
}
