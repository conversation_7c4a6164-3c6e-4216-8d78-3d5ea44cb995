package com.vincenttho.pr.scheduler.storage

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.readValue
import com.vincenttho.pr.scheduler.config.ScheduleHubProperties
import com.vincenttho.pr.scheduler.entity.SchedulerConfig
import com.vincenttho.pr.scheduler.entity.SchedulerExecution
import jakarta.annotation.PostConstruct
import org.slf4j.LoggerFactory
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import java.io.File
import java.time.LocalDateTime
import java.util.concurrent.ConcurrentHashMap

/**
 * JSON file-based storage provider for schedulers
 * <AUTHOR>
 */
class JsonFileStorageProvider(private val properties: ScheduleHubProperties) : SchedulerStorageProvider {
    private val logger = LoggerFactory.getLogger(JsonFileStorageProvider::class.java)

    private val objectMapper = ObjectMapper().apply {
        registerModule(JavaTimeModule())
        disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
    }

    private val schedulers = ConcurrentHashMap<String, SchedulerConfig>()
    private val executions = ConcurrentHashMap<String, MutableList<SchedulerExecution>>()
    // 不再需要 ID 计数器，因为我们使用 UUID

    private val storageDir = File(properties.storagePath)
    private val schedulersFile = File(storageDir, "schedulers.json")
    private val executionsFile = File(storageDir, "executions.json")

    @PostConstruct
    fun init() {
        // Create storage directory if it doesn't exist
        if (!storageDir.exists()) {
            storageDir.mkdirs()
        }

        // Load schedulers from file
        if (schedulersFile.exists()) {
            try {
                val loadedSchedulers: List<SchedulerConfig> = objectMapper.readValue(schedulersFile)
                loadedSchedulers.forEach { scheduler ->
                    schedulers[scheduler.id] = scheduler
                }
                logger.info("Loaded {} schedulers from {}", loadedSchedulers.size, schedulersFile.absolutePath)
            } catch (e: Exception) {
                logger.error("Error loading schedulers from {}: {}", schedulersFile.absolutePath, e.message, e)
            }
        }

        // Load executions from file
        if (executionsFile.exists()) {
            try {
                val loadedExecutions: List<SchedulerExecution> = objectMapper.readValue(executionsFile)

                // 加载执行记录
                loadedExecutions.forEach { execution ->

                    // Group executions by scheduler ID
                    val schedulerExecutions = executions.computeIfAbsent(execution.schedulerId) { mutableListOf() }
                    schedulerExecutions.add(execution)
                }

                logger.info("Loaded {} executions from {}", loadedExecutions.size, executionsFile.absolutePath)
            } catch (e: Exception) {
                logger.error("Error loading executions from {}: {}", executionsFile.absolutePath, e.message, e)
            }
        }
    }

    /**
     * Save schedulers to file
     */
    private fun saveSchedulersToFile() {
        try {
            objectMapper.writeValue(schedulersFile, schedulers.values.toList())
        } catch (e: Exception) {
            logger.error("Error saving schedulers to {}: {}", schedulersFile.absolutePath, e.message, e)
        }
    }

    /**
     * Save executions to file
     */
    private fun saveExecutionsToFile() {
        try {
            val allExecutions = executions.values.flatten()
            objectMapper.writeValue(executionsFile, allExecutions)
        } catch (e: Exception) {
            logger.error("Error saving executions to {}: {}", executionsFile.absolutePath, e.message, e)
        }
    }

    override fun findAllSchedulers(): List<SchedulerConfig> {
        return schedulers.values.toList()
    }

    override fun findSchedulerById(id: String): SchedulerConfig? {
        return schedulers[id]
    }

    override fun saveScheduler(config: SchedulerConfig): SchedulerConfig {
        // Update the updatedAt field
        val updatedConfig = config.copy(updatedAt = LocalDateTime.now())

        // Save to memory
        schedulers[updatedConfig.id] = updatedConfig

        // Save to file
        saveSchedulersToFile()

        return updatedConfig
    }

    override fun deleteScheduler(id: String) {
        // Remove from memory
        schedulers.remove(id)

        // Save to file
        saveSchedulersToFile()
    }

    override fun schedulerExists(id: String): Boolean {
        return schedulers.containsKey(id)
    }

    override fun saveExecution(execution: SchedulerExecution): SchedulerExecution {
        // 确保执行记录有 ID
        val executionWithId = execution

        // Save to memory
        val schedulerExecutions = executions.computeIfAbsent(executionWithId.schedulerId) { mutableListOf() }

        // Remove existing execution with the same ID if it exists
        schedulerExecutions.removeIf { it.id == executionWithId.id }

        schedulerExecutions.add(executionWithId)

        // Save to file
        saveExecutionsToFile()

        return executionWithId
    }

    override fun findExecutionsBySchedulerId(schedulerId: String, pageable: Pageable): Page<SchedulerExecution> {
        val schedulerExecutions = executions[schedulerId] ?: emptyList()

        // Sort by start time descending
        val sortedExecutions = schedulerExecutions.sortedByDescending { it.startTime }

        // Apply pagination
        val start = pageable.pageNumber * pageable.pageSize
        val end = minOf(start + pageable.pageSize, sortedExecutions.size)

        val pagedContent = if (start < sortedExecutions.size) {
            sortedExecutions.subList(start, end)
        } else {
            emptyList()
        }

        return PageImpl(pagedContent, pageable, sortedExecutions.size.toLong())
    }

    override fun updateNextQueryStartTime(schedulerConfig: SchedulerConfig, nextQueryStartTime: LocalDateTime) {
        // TODO 更新json中调度器配置的nextQueryStartTime字段
    }
}
