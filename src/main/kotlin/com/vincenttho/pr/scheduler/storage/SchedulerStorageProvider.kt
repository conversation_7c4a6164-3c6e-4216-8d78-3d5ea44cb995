package com.vincenttho.pr.scheduler.storage

import com.vincenttho.pr.scheduler.entity.SchedulerConfig
import com.vincenttho.pr.scheduler.entity.SchedulerExecution
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import java.time.LocalDateTime

/**
 * Interface for scheduler storage providers
 * <AUTHOR>
 */
interface SchedulerStorageProvider {
    
    /**
     * Find all scheduler configurations
     * @return List of scheduler configurations
     */
    fun findAllSchedulers(): List<SchedulerConfig>
    
    /**
     * Find a scheduler configuration by ID
     * @param id Scheduler ID
     * @return Scheduler configuration or null if not found
     */
    fun findSchedulerById(id: String): SchedulerConfig?
    
    /**
     * Save a scheduler configuration
     * @param config Scheduler configuration
     * @return Saved scheduler configuration
     */
    fun saveScheduler(config: SchedulerConfig): SchedulerConfig
    
    /**
     * Delete a scheduler configuration
     * @param id Scheduler ID
     */
    fun deleteScheduler(id: String)
    
    /**
     * Check if a scheduler exists
     * @param id Scheduler ID
     * @return True if the scheduler exists, false otherwise
     */
    fun schedulerExists(id: String): Boolean
    
    /**
     * Save a scheduler execution
     * @param execution Scheduler execution
     * @return Saved scheduler execution
     */
    fun saveExecution(execution: SchedulerExecution): SchedulerExecution
    
    /**
     * Find scheduler executions by scheduler ID
     * @param schedulerId Scheduler ID
     * @param pageable Pageable
     * @return Page of scheduler executions
     */
    fun findExecutionsBySchedulerId(schedulerId: String, pageable: Pageable): Page<SchedulerExecution>

    /**
     * 更新下一个查询开始时间
     * <AUTHOR>
     * @date 2025/05/10
     * @param [schedulerConfig] 调度程序配置
     * @param [nextQueryStartTime] 下一个查询开始时间
     */
    fun updateNextQueryStartTime(schedulerConfig: SchedulerConfig, nextQueryStartTime: LocalDateTime)

}
