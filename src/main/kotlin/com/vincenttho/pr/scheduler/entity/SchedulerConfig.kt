package com.vincenttho.pr.scheduler.entity

import jakarta.persistence.*
import java.time.LocalDateTime

/**
 * Scheduler configuration entity
 * <AUTHOR>
 */
@Entity
@Table(name = "t_pr_scheduler_configs")
data class SchedulerConfig(
    @Id
    @Column(length = 100)
    var id: String,

    @Column(nullable = false, length = 100)
    var name: String,

    @Column(length = 500)
    var description: String?,

    @Column(nullable = false, length = 100)
    var cronExpression: String,

    @Column(nullable = false)
    var enabled: Boolean = true,

    @Column
    var sql: String = "",

    @Column
    var nextQueryStartTime: LocalDateTime? = LocalDateTime.now(),

    @Column
    var wxApiKey: String = "",

    @Column
    var lastExecutionTime: LocalDateTime? = null,

    @Column
    var nextExecutionTime: LocalDateTime? = null,

    @Column(nullable = false)
    var createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now(),

    @Column
    var messageTitle: String? = "",

    /** 对应SchedulerDsConfig的id */
    @Column
    var dataSourceId: String? = null,
) {
    constructor(): this(
        id = "",
        name = "",
        description = "",
        cronExpression = "",
    )
}
