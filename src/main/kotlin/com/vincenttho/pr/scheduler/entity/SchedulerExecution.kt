package com.vincenttho.pr.scheduler.entity

import jakarta.persistence.*
import java.time.LocalDateTime
import java.util.UUID

/**
 * Scheduler execution history
 * <AUTHOR>
 */
@Entity
@Table(name = "t_pr_scheduler_executions")
data class SchedulerExecution(
    @Id
    @Column(length = 36)
    val id: String = UUID.randomUUID().toString(),

    @Column(nullable = false, length = 100)
    val schedulerId: String,

    @Column(nullable = false)
    val startTime: LocalDateTime,

    @Column
    var endTime: LocalDateTime? = null,

    @Column
    var success: Boolean? = null,

    @Column(length = 1000)
    var message: String? = null
) {
    constructor(): this(
        id = UUID.randomUUID().toString(),
        schedulerId = "",
        startTime = LocalDateTime.now()
    )
}
