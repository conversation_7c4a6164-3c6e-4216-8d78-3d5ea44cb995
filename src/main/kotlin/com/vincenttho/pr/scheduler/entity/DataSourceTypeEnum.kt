package com.vincenttho.pr.scheduler.entity

/**
 *
 * 数据源类型枚举
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
enum class DataSourceTypeEnum(val jdbcUrlTemplate: String, val driver: String) {
    /** oracle  */
    ORACLE("jdbc:oracle:thin:@<<host>>:<<port>>/<<instance>>", "oracle.jdbc.OracleDriver"),

    /** mysql  */
    MYSQL("jdbc:mysql://<<host>>:<<port>>/<<instance>>?Unicode=true&characterEncoding=UTF-8", "com.mysql.jdbc.Driver"),

    /** sql server  */
    SQL_SERVER(
        "jdbc:sqlserver://<<host>>:<<port>>;DatabaseName=<<instance>>",
        "com.microsoft.sqlserver.jdbc.SQLServerDriver"
    )
}