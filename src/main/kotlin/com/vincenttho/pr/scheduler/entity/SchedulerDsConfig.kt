package com.vincenttho.pr.scheduler.entity

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.time.LocalDateTime

/**
 * <p>Data source configuration</p>
 * <AUTHOR>
 * @date 2025-06-06
 */
@Entity
@Table(name = "t_pr_scheduler_ds_config")
data class SchedulerDsConfig(
    @Id
    @Column(length = 100)
    var id: String,

    var dataSourceName: String,

    @Column(nullable = false)
    var dataSourceType: DataSourceTypeEnum,

    @Column(nullable = false, length = 200)
    var dataSourceHost: String,

    @Column(nullable = false, length = 10)
    var dataSourcePort: String,

    @Column(nullable = false, length = 100)
    var dataSourceInstance: String,

    @Column(nullable = false, length = 100)
    var dataSourceUser: String,

    @Column(nullable = false, length = 200)
    var dataSourcePassword: String,

    @Column(length = 500)
    var description: String? = null,

    @Column(nullable = false)
    var createdAt: LocalDateTime = LocalDateTime.now(),

    @Column(nullable = false)
    var updatedAt: LocalDateTime = LocalDateTime.now()
) {
    constructor(): this(
        id = "",
        dataSourceName = "",
        dataSourceType = DataSourceTypeEnum.ORACLE,
        dataSourceHost = "",
        dataSourcePort = "",
        dataSourceInstance = "",
        dataSourceUser = "",
        dataSourcePassword = ""
    )

    /**
     * 构建JDBC URL
     */
    fun buildJdbcUrl(): String {
        return dataSourceType.jdbcUrlTemplate
            .replace("<<host>>", dataSourceHost)
            .replace("<<port>>", dataSourcePort)
            .replace("<<instance>>", dataSourceInstance)
    }
}