package com.vincenttho.pr.scheduler.entity

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table

/**
 * <p>Data source configuration</p>
 * <AUTHOR>
 * @date 2025-06-06
 */
@Entity
@Table(name = "t_pr_scheduler_ds_config")
class SchedulerDsConfig {
    @Id
    @Column
    val id: String? = null
    @Column
    val dataSourceType: DataSourceTypeEnum? = null
    @Column
    val dataSourceHost: String? = null
    @Column
    val dataSourcePort: String? = null
    @Column
    val dataSourceInstance: String? = null
    @Column
    val dataSourceUser: String? = null
    @Column
    val dataSourcePassword: String? = null
    @Column
    val isUpdate: String? = null
}