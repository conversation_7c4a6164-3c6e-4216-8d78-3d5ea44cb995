package com.vincenttho.pr.scheduler.service

import com.vincenttho.pr.helper.MessageSendHelper
import com.vincenttho.pr.scheduler.entity.SchedulerConfig
import com.vincenttho.pr.scheduler.entity.SchedulerExecution
import com.vincenttho.pr.scheduler.storage.SchedulerStorageProvider
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.jdbc.core.RowMapper
import org.springframework.stereotype.Service
import java.sql.ResultSet
import java.time.LocalDateTime

/**
 * <p>通用调度器业务类</p>
 * <AUTHOR>
 * @date 2025-05-10
 */
@Service
class CommonSchedulerService(
    private val jdbcTemplate: JdbcTemplate,
    private val messageSendHelper: MessageSendHelper,
    private val schedulerManagerService: SchedulerManagerService,
    private val storageProvider: SchedulerStorageProvider,
) {
    private val logger = LoggerFactory.getLogger(CommonSchedulerService::class.java)

    @Value("\${debug.mode:false}")
    private var debugMode: Boolean = false

    fun run(scheduler: SchedulerConfig) {
        val startTime = LocalDateTime.now()
        var count = 0
        val originQueryStartTime = scheduler.nextQueryStartTime
        logger.info("本次查询开始时间：${scheduler.nextQueryStartTime}")

        // TODO 多数据源待实现
        // TODO 如果scheduler.dataSourceId不为空，先查询这个id是否已经创建数据源。如有直接切换；如无，下一步；
        // TODO 通过scheduler.dataSourceId查询SchedulerDsConfig
        // TODO 根据查询到的数据创建数据源并切换数据源查询

        val message = jdbcTemplate.query(querySql(scheduler), CommonSchedulerRowMapper(), scheduler.nextQueryStartTime)
            .joinToString("\n") { "${++count} ${it}" }

        // TODO 查询数据后切回主数据源

        if (message.isNotEmpty()) {
            messageSendHelper.sendMessage("保健对象提醒：\n ${message}", scheduler.wxApiKey!!)
        }
        logger.info("查询到${count}条数据。")


        schedulerManagerService.updateNextQueryStartTime(scheduler, startTime)
        scheduler.nextQueryStartTime = startTime

        var messageLog = ""
        if(debugMode) {
            messageLog = message
        }

        // 保存日志
        val execution = SchedulerExecution(
            schedulerId = scheduler.id,
            startTime = startTime,
            endTime = LocalDateTime.now(),
            success = true,
            message = "执行成功，本次查询到${count}条数据，开始时间查询条件为${originQueryStartTime} \n ${messageLog}"
        )
        storageProvider.saveExecution(execution)

    }

    fun querySql(scheduler: SchedulerConfig): String {
        return """
            select vv.message from (
                ${scheduler.sql}
            ) vv
            where vv.query_time >= ?
        """.trimIndent()
    }

    private class CommonSchedulerRowMapper : RowMapper<String> {
        override fun mapRow(rs: ResultSet, rowNum: Int): String? {
            return rs.getString("message")
        }
    }

}