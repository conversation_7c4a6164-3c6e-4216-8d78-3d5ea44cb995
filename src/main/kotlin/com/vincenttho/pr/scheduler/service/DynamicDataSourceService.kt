package com.vincenttho.pr.scheduler.service

import com.vincenttho.pr.scheduler.entity.SchedulerDsConfig
import com.vincenttho.pr.scheduler.repository.SchedulerDsConfigRepository
import org.slf4j.LoggerFactory
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.jdbc.datasource.DriverManagerDataSource
import org.springframework.stereotype.Service
import java.util.concurrent.ConcurrentHashMap
import javax.sql.DataSource

/**
 * 动态数据源管理服务
 * <AUTHOR>
 */
@Service
class DynamicDataSourceService(
    private val schedulerDsConfigRepository: SchedulerDsConfigRepository,
    private val primaryJdbcTemplate: JdbcTemplate
) {
    private val logger = LoggerFactory.getLogger(DynamicDataSourceService::class.java)
    
    // 缓存已创建的数据源
    private val dataSourceCache = ConcurrentHashMap<String, DataSource>()
    private val jdbcTemplateCache = ConcurrentHashMap<String, JdbcTemplate>()
    
    /**
     * 根据数据源ID获取JdbcTemplate
     * @param dataSourceId 数据源ID
     * @return JdbcTemplate，如果获取失败返回null
     */
    fun getJdbcTemplate(dataSourceId: String): JdbcTemplate? {
        return try {
            // 先从缓存中获取
            jdbcTemplateCache[dataSourceId]?.let { return it }
            
            // 查询数据源配置
            val dsConfig = schedulerDsConfigRepository.findById(dataSourceId).orElse(null)
                ?: run {
                    logger.warn("数据源配置不存在: {}", dataSourceId)
                    return null
                }
            
            // 创建数据源
            val dataSource = createDataSource(dsConfig)
            
            // 创建JdbcTemplate
            val jdbcTemplate = JdbcTemplate(dataSource)
            
            // 测试连接
            testConnection(jdbcTemplate)
            
            // 缓存数据源和JdbcTemplate
            dataSourceCache[dataSourceId] = dataSource
            jdbcTemplateCache[dataSourceId] = jdbcTemplate
            
            logger.info("成功创建并缓存数据源: {}", dataSourceId)
            jdbcTemplate
        } catch (e: Exception) {
            logger.error("创建数据源失败: {}, 错误: {}", dataSourceId, e.message, e)
            null
        }
    }
    
    /**
     * 获取主数据源的JdbcTemplate
     */
    fun getPrimaryJdbcTemplate(): JdbcTemplate {
        return primaryJdbcTemplate
    }
    
    /**
     * 创建数据源
     */
    private fun createDataSource(dsConfig: SchedulerDsConfig): DataSource {
        val dataSource = DriverManagerDataSource()
        dataSource.setDriverClassName(dsConfig.dataSourceType.driver)
        dataSource.url = dsConfig.buildJdbcUrl()
        dataSource.username = dsConfig.dataSourceUser
        dataSource.password = dsConfig.dataSourcePassword
        
        logger.info("创建数据源: {} -> {}", dsConfig.id, dataSource.url)
        return dataSource
    }
    
    /**
     * 测试数据源连接
     */
    private fun testConnection(jdbcTemplate: JdbcTemplate, dsConfig: SchedulerDsConfig) {
        val sql = when(dsConfig.dataSourceType) {
            // 假设 dataSourceType 是枚举类，包含 ORACLE、MYSQL、SQLSERVER 等
            DataSourceType.ORACLE -> "SELECT 1 FROM DUAL"
            DataSourceType.MYSQL, DataSourceType.SQLSERVER -> "SELECT 1"
            else -> throw IllegalArgumentException("Unsupported database type")
        }

        jdbcTemplate.execute(sql)
        logger.debug("数据源连接测试成功: {}", dsConfig.dataSourceType)
    }
    
    /**
     * 清除指定数据源的缓存
     */
    fun clearCache(dataSourceId: String) {
        dataSourceCache.remove(dataSourceId)
        jdbcTemplateCache.remove(dataSourceId)
        logger.info("清除数据源缓存: {}", dataSourceId)
    }
    
    /**
     * 清除所有数据源缓存
     */
    fun clearAllCache() {
        dataSourceCache.clear()
        jdbcTemplateCache.clear()
        logger.info("清除所有数据源缓存")
    }
    
    /**
     * 检查数据源是否已缓存
     */
    fun isCached(dataSourceId: String): Boolean {
        return jdbcTemplateCache.containsKey(dataSourceId)
    }
}
