package com.vincenttho.pr.scheduler.service

import com.vincenttho.pr.scheduler.entity.SchedulerDsConfig
import com.vincenttho.pr.scheduler.repository.SchedulerDsConfigRepository
import org.slf4j.LoggerFactory
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

/**
 * 调度器数据源配置管理服务
 * <AUTHOR>
 */
@Service
class SchedulerDsConfigService(
    private val schedulerDsConfigRepository: SchedulerDsConfigRepository,
    private val dynamicDataSourceService: DynamicDataSourceService
) {
    private val logger = LoggerFactory.getLogger(SchedulerDsConfigService::class.java)
    
    /**
     * 获取所有数据源配置
     */
    fun findAll(): List<SchedulerDsConfig> {
        return schedulerDsConfigRepository.findAll()
    }
    
    /**
     * 分页获取数据源配置
     */
    fun findAll(pageable: Pageable): Page<SchedulerDsConfig> {
        return schedulerDsConfigRepository.findAll(pageable)
    }
    
    /**
     * 根据ID获取数据源配置
     */
    fun findById(id: String): SchedulerDsConfig? {
        return schedulerDsConfigRepository.findById(id).orElse(null)
    }
    
    /**
     * 保存数据源配置
     */
    @Transactional
    fun save(config: SchedulerDsConfig): SchedulerDsConfig {
        val savedConfig = if (schedulerDsConfigRepository.existsById(config.id)) {
            // 更新现有配置
            val existingConfig = schedulerDsConfigRepository.findById(config.id).get()
            val updatedConfig = config.copy(
                createdAt = existingConfig.createdAt,
                updatedAt = LocalDateTime.now()
            )
            schedulerDsConfigRepository.save(updatedConfig)
        } else {
            // 创建新配置
            schedulerDsConfigRepository.save(config)
        }
        
        // 清除缓存，强制重新创建数据源
        dynamicDataSourceService.clearCache(config.id)
        logger.info("保存数据源配置: {}", config.id)
        
        return savedConfig
    }
    
    /**
     * 删除数据源配置
     */
    @Transactional
    fun deleteById(id: String) {
        if (schedulerDsConfigRepository.existsById(id)) {
            schedulerDsConfigRepository.deleteById(id)
            // 清除缓存
            dynamicDataSourceService.clearCache(id)
            logger.info("删除数据源配置: {}", id)
        } else {
            throw IllegalArgumentException("数据源配置不存在: $id")
        }
    }
    
    /**
     * 检查数据源配置是否存在
     */
    fun existsById(id: String): Boolean {
        return schedulerDsConfigRepository.existsById(id)
    }
    
    /**
     * 测试数据源连接
     */
    fun testConnection(id: String): Boolean {
        return try {
            val jdbcTemplate = dynamicDataSourceService.getJdbcTemplate(id)
            jdbcTemplate != null
        } catch (e: Exception) {
            logger.error("测试数据源连接失败: {}, 错误: {}", id, e.message)
            false
        }
    }
    
    /**
     * 测试数据源配置连接（不保存）
     */
    fun testConnection(config: SchedulerDsConfig): Boolean {
        return try {
            // 临时创建数据源进行测试
            val tempService = DynamicDataSourceService(schedulerDsConfigRepository, dynamicDataSourceService.getPrimaryJdbcTemplate())
            // 这里需要一个临时的测试方法，暂时返回true
            // 实际实现中可以创建临时数据源进行连接测试
            true
        } catch (e: Exception) {
            logger.error("测试数据源配置连接失败: {}, 错误: {}", config.id, e.message)
            false
        }
    }
}
