package com.vincenttho.pr.scheduler.service

import com.vincenttho.pr.scheduler.entity.SchedulerConfig
import com.vincenttho.pr.scheduler.entity.SchedulerExecution
import com.vincenttho.pr.scheduler.storage.JpaStorageProvider
import com.vincenttho.pr.scheduler.storage.SchedulerStorageProvider
import jakarta.annotation.PostConstruct
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Lazy
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.scheduling.TaskScheduler
import org.springframework.scheduling.support.CronTrigger
import org.springframework.scheduling.support.SimpleTriggerContext
import org.springframework.stereotype.Service
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ScheduledFuture

/**
 * Service for managing schedulers
 * <AUTHOR>
 */
@Service
class SchedulerManagerService(
    private val storageProvider: SchedulerStorageProvider,
    private val taskScheduler: TaskScheduler,
    @Lazy
    private val commonSchedulerService: CommonSchedulerService,
) {
    private val logger = LoggerFactory.getLogger(SchedulerManagerService::class.java)
    private val scheduledTasks = ConcurrentHashMap<String, ScheduledFuture<*>>()
    private val taskMap = ConcurrentHashMap<String, Runnable>()

    @PostConstruct
    fun init() {
        // Start all enabled schedulers
        storageProvider.findAllSchedulers()
            .filter { it.enabled }
            .forEach { scheduleTask(it) }
    }

    /**
     * Register a task for a scheduler
     * @param defaultConfig Default scheduler configuration to use if not found in storage
     * @param task Task to run
     */
    fun registerTask(schedulerConfig: SchedulerConfig, task: Runnable) {
        registerTask(schedulerConfig.id, schedulerConfig, task)
    }

    /**
     * Register a task for a scheduler
     * @param schedulerId Scheduler ID
     * @param defaultConfig Default scheduler configuration to use if not found in storage
     * @param task Task to run
     */
    fun registerTask(schedulerId: String, defaultConfig: SchedulerConfig? = null, task: Runnable) {
        taskMap[schedulerId] = task
        logger.info("Registered task for scheduler: {}", schedulerId)

        // Check if the scheduler exists in storage
        val existingConfig = storageProvider.findSchedulerById(schedulerId)

        if (existingConfig != null) {
            // Use the existing configuration from storage
            logger.info("Using existing configuration for scheduler: {}", schedulerId)
            if (existingConfig.enabled) {
                scheduleTask(existingConfig)
            }
        } else if (defaultConfig != null) {
            // Create a new scheduler with the default configuration
            logger.info("Creating new scheduler with default configuration: {}", schedulerId)
            try {
                val savedConfig = storageProvider.saveScheduler(defaultConfig)
                if (savedConfig.enabled) {
                    scheduleTask(savedConfig)
                }
            } catch (e: Exception) {
                logger.error("Error creating scheduler {}: {}", schedulerId, e.message, e)
            }
        }
    }

    /**
     * Register a task for a scheduler (simplified version)
     * @param schedulerId Scheduler ID
     * @param task Task to run
     */
    fun registerTask(schedulerId: String, task: Runnable) {
        registerTask(schedulerId, null, task)
    }

    /**
     * Create a task for a scheduler using the latest configuration
     * @param config Scheduler configuration
     * @return Task to run
     */
    private fun createTask(config: SchedulerConfig): Runnable {
        return Runnable {
            commonSchedulerService.run(config)
        }
    }

    /**
     * Get all scheduler configurations
     * @return List of scheduler configurations
     */
    fun getAllSchedulers(): List<SchedulerConfig> {
        val schedulers = storageProvider.findAllSchedulers()

        // Update next execution times
        schedulers.forEach { scheduler ->
            if (scheduler.enabled) {
                scheduler.nextExecutionTime = calculateNextExecutionTime(scheduler.cronExpression)
            }
        }

        return schedulers
    }

    /**
     * Get scheduler by ID
     * @param id Scheduler ID
     * @return Scheduler configuration or null if not found
     */
    fun getSchedulerById(id: String): SchedulerConfig? {
        val scheduler = storageProvider.findSchedulerById(id)

        // Update next execution time
        if (scheduler != null && scheduler.enabled) {
            scheduler.nextExecutionTime = calculateNextExecutionTime(scheduler.cronExpression)
        }

        return scheduler
    }

    /**
     * Create a new scheduler
     * @param config Scheduler configuration
     * @return Created scheduler configuration
     */
    fun createScheduler(config: SchedulerConfig): SchedulerConfig {
        // Check if scheduler already exists
        if (storageProvider.schedulerExists(config.id)) {
            throw IllegalArgumentException("Scheduler with ID ${config.id} already exists")
        }

        // Save the scheduler
        val savedConfig = storageProvider.saveScheduler(config)

        // Register the task if it's enabled
        if (savedConfig.enabled) {
            registerTask(savedConfig) {
                commonSchedulerService.run(savedConfig)
            }
        }

        return savedConfig
    }

    /**
     * Update a scheduler
     * @param config Scheduler configuration
     * @return Updated scheduler configuration
     */
    fun updateScheduler(config: SchedulerConfig): SchedulerConfig {
        // Check if scheduler exists
        val existingConfig = storageProvider.findSchedulerById(config.id)
            ?: throw IllegalArgumentException("Scheduler not found: ${config.id}")

        // Update fields
        val updatedConfig = existingConfig.copy(
            name = config.name,
            description = config.description,
            cronExpression = config.cronExpression,
            enabled = config.enabled,
            sql = config.sql,
            wxApiKey = config.wxApiKey,
            dataSourceId = config.dataSourceId,
            updatedAt = LocalDateTime.now()
        )

        // Save the updated scheduler
        val savedConfig = storageProvider.saveScheduler(updatedConfig)

        // Always stop the existing task if it's running
        stopTask(savedConfig.id)

        // Reschedule the task if it's enabled
        if (savedConfig.enabled) {
            // Schedule the task with the new configuration
            scheduleTask(savedConfig)
            logger.info("Rescheduled task for scheduler: ${savedConfig.id} with updated configuration")
        } else {
            logger.info("Scheduler ${savedConfig.id} is disabled, not scheduling task")
        }

        return savedConfig
    }

    /**
     * Delete a scheduler
     * @param id Scheduler ID
     */
    fun deleteScheduler(id: String) {
        // Stop the task if it's running
        stopTask(id)

        // Delete the scheduler
        storageProvider.deleteScheduler(id)
    }

    /**
     * Start a scheduler
     * @param id Scheduler ID
     * @return Updated scheduler configuration
     */
    fun startScheduler(id: String): SchedulerConfig {
        val scheduler = storageProvider.findSchedulerById(id)
            ?: throw IllegalArgumentException("Scheduler not found: $id")

        val updatedScheduler = scheduler.copy(
            enabled = true,
            updatedAt = LocalDateTime.now()
        )

        val savedScheduler = storageProvider.saveScheduler(updatedScheduler)

        // Schedule the task if it has a registered task
        if (taskMap.containsKey(id)) {
            scheduleTask(savedScheduler)
        } else {
            logger.warn("No task registered for scheduler: {}", id)
        }

        return savedScheduler
    }

    /**
     * Pause a scheduler
     * @param id Scheduler ID
     * @return Updated scheduler configuration
     */
    fun pauseScheduler(id: String): SchedulerConfig {
        val scheduler = storageProvider.findSchedulerById(id)
            ?: throw IllegalArgumentException("Scheduler not found: $id")

        val updatedScheduler = scheduler.copy(
            enabled = false,
            updatedAt = LocalDateTime.now()
        )

        val savedScheduler = storageProvider.saveScheduler(updatedScheduler)

        // Stop the task
        stopTask(id)

        return savedScheduler
    }

    /**
     * Run a scheduler immediately
     * @param id Scheduler ID
     */
    fun runSchedulerNow(id: String) {

        val queryStartTime = LocalDateTime.now().plusSeconds(-3L)

        val scheduler = storageProvider.findSchedulerById(id)
            ?: throw IllegalArgumentException("Scheduler not found: $id")

        // Create execution record
        val execution = SchedulerExecution(
            schedulerId = id,
            startTime = LocalDateTime.now()
        )

        try {
            logger.info("Running scheduler immediately: {}", id)
            commonSchedulerService.run(scheduler)

            // Update scheduler
            val updatedScheduler = scheduler.copy(
                lastExecutionTime = execution.startTime,
                updatedAt = LocalDateTime.now(),
                nextQueryStartTime = queryStartTime
            )
            storageProvider.saveScheduler(updatedScheduler)
        } catch (e: Exception) {
            logger.error("Error running scheduler {}: {}", id, e.message, e)

            // Update execution record
            val updatedExecution = execution.copy(
                endTime = LocalDateTime.now(),
                success = false,
                message = "Error: ${e.message}"
            )
            storageProvider.saveExecution(updatedExecution)

            throw e
        }
    }

    /**
     * Get scheduler executions
     * @param schedulerId Scheduler ID
     * @param pageable Pageable
     * @return Page of scheduler executions
     */
    fun getSchedulerExecutions(schedulerId: String, pageable: Pageable): Page<SchedulerExecution> {
        return storageProvider.findExecutionsBySchedulerId(schedulerId, pageable)
    }

    /**
     * Get scheduler executions by time range
     * @param schedulerId Scheduler ID
     * @param startTime Start time
     * @param endTime End time
     * @param pageable Pageable
     * @return Page of scheduler executions
     */
    fun getSchedulerExecutionsByTimeRange(
        schedulerId: String,
        startTime: LocalDateTime,
        endTime: LocalDateTime,
        pageable: Pageable
    ): Page<SchedulerExecution> {
        return if (storageProvider is JpaStorageProvider) {
            // Use the custom repository method for JPA storage
            val repository = storageProvider.getSchedulerExecutionRepository()
            repository.findBySchedulerIdAndTimeRangeWithLegacyPagination(schedulerId, startTime, endTime, pageable)
        } else {
            // For other storage providers, filter in memory
            val allExecutions = storageProvider.findExecutionsBySchedulerId(schedulerId, Pageable.unpaged())
            val filteredExecutions = allExecutions.content.filter {
                it.startTime.isAfter(startTime) && it.startTime.isBefore(endTime)
            }

            // Apply pagination manually
            val start = pageable.pageNumber * pageable.pageSize
            val end = minOf(start + pageable.pageSize, filteredExecutions.size)
            val pagedContent = if (start < filteredExecutions.size) {
                filteredExecutions.subList(start, end)
            } else {
                emptyList()
            }

            PageImpl(pagedContent, pageable, filteredExecutions.size.toLong())
        }
    }

    /**
     * Schedule a task
     * @param config Scheduler configuration
     */
    private fun scheduleTask(config: SchedulerConfig) {
        // Check if the task is already scheduled
        stopTask(config.id)

        // Create a new task with the latest configuration
        val task = createTask(config)

        // Update the task map
        taskMap[config.id] = task

        try {
            logger.info("Scheduling task for scheduler: {}", config.id)

            // Create a wrapper task that updates the execution history
            val wrappedTask = Runnable {
                // Create execution record
                val execution = SchedulerExecution(
                    schedulerId = config.id,
                    startTime = LocalDateTime.now()
                )

                try {
                    task.run()

                    // Update scheduler
                    val scheduler = storageProvider.findSchedulerById(config.id)
                    if (scheduler != null) {
                        val updatedScheduler = scheduler.copy(
                            lastExecutionTime = execution.startTime,
                            updatedAt = LocalDateTime.now()
                        )
                        storageProvider.saveScheduler(updatedScheduler)
                    }
                } catch (e: Exception) {
                    logger.error("Error executing scheduled task for {}: {}", config.id, e.message, e)

                    // Update execution record
                    val updatedExecution = execution.copy(
                        endTime = LocalDateTime.now(),
                        success = false,
                        message = "Error: ${e.message}"
                    )
                    storageProvider.saveExecution(updatedExecution)
                }
            }

            // Schedule the task
            val trigger = CronTrigger(config.cronExpression)
            val scheduledTask = taskScheduler.schedule(wrappedTask, trigger)

            // Store the scheduled task
            scheduledTasks[config.id] = scheduledTask

            // Update next execution time
            val nextExecutionTime = calculateNextExecutionTime(config.cronExpression)
            val updatedConfig = config.copy(nextExecutionTime = nextExecutionTime)
            storageProvider.saveScheduler(updatedConfig)

            logger.info("Scheduled task for scheduler: {} with next execution at {}", config.id, nextExecutionTime)
        } catch (e: Exception) {
            logger.error("Error scheduling task for {}: {}", config.id, e.message, e)
            throw e
        }
    }

    /**
     * Stop a task
     * @param id Scheduler ID
     */
    private fun stopTask(id: String) {
        val scheduledTask = scheduledTasks.remove(id)
        if (scheduledTask != null) {
            logger.info("Stopping task for scheduler: {}", id)
            scheduledTask.cancel(false)
        }
    }

    /**
     * Calculate next execution time
     * @param cronExpression Cron expression
     * @return Next execution time
     */
    private fun calculateNextExecutionTime(cronExpression: String): LocalDateTime? {
        try {
            val trigger = CronTrigger(cronExpression)
            val triggerContext = SimpleTriggerContext()
            val nextExecutionTime = trigger.nextExecutionTime(triggerContext)
            return if (nextExecutionTime != null) {
                LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(nextExecutionTime.time),
                    ZoneId.systemDefault()
                )
            } else {
                null
            }
        } catch (e: Exception) {
            logger.error("Error calculating next execution time for cron expression {}: {}", cronExpression, e.message, e)
            return null
        }
    }

    fun updateNextQueryStartTime(schedulerConfig: SchedulerConfig, nextQueryStartTime: LocalDateTime) {
        storageProvider.updateNextQueryStartTime(schedulerConfig, nextQueryStartTime)
    }
}
