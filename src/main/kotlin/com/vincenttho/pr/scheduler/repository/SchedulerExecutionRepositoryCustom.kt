package com.vincenttho.pr.scheduler.repository

import com.vincenttho.pr.scheduler.entity.SchedulerExecution
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import java.time.LocalDateTime

/**
 * Custom repository interface for scheduler executions
 * <AUTHOR>
 */
interface SchedulerExecutionRepositoryCustom {
    /**
     * Find executions by scheduler ID with custom pagination for older Oracle versions
     * @param schedulerId Scheduler ID
     * @param pageable Pageable
     * @return Page of scheduler executions
     */
    fun findBySchedulerIdOrderByStartTimeDescWithLegacyPagination(schedulerId: String, pageable: Pageable): Page<SchedulerExecution>

    /**
     * Find executions by scheduler ID and time range with custom pagination for older Oracle versions
     * @param schedulerId Scheduler ID
     * @param startTime Start time
     * @param endTime End time
     * @param pageable Pageable
     * @return Page of scheduler executions
     */
    fun findBySchedulerIdAndTimeRangeWithLegacyPagination(
        schedulerId: String,
        startTime: LocalDateTime,
        endTime: LocalDateTime,
        pageable: Pageable
    ): Page<SchedulerExecution>
}
