package com.vincenttho.pr.scheduler.repository

import com.vincenttho.pr.scheduler.entity.SchedulerConfig
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

/**
 * Repository for scheduler configurations
 * <AUTHOR>
 */
@Repository
interface SchedulerConfigRepository : JpaRepository<SchedulerConfig, String> {
    @Modifying
    @Transactional
    @Query("update SchedulerConfig set nextQueryStartTime = :nextQueryStartTime where id = :id")
    fun updateNextQueryStartTime(id: String, nextQueryStartTime: LocalDateTime)
}
