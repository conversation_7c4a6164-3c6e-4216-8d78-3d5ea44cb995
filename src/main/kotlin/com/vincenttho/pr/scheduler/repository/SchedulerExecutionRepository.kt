package com.vincenttho.pr.scheduler.repository

import com.vincenttho.pr.scheduler.entity.SchedulerExecution
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

/**
 * Repository for scheduler executions
 * <AUTHOR>
 */
@Repository
interface SchedulerExecutionRepository : JpaRepository<SchedulerExecution, String>, SchedulerExecutionRepositoryCustom {

    /**
     * Find executions by scheduler ID
     * @param schedulerId Scheduler ID
     * @param pageable Pageable
     * @return Page of scheduler executions
     */
    fun findBySchedulerIdOrderByStartTimeDesc(schedulerId: String, pageable: Pageable): Page<SchedulerExecution>
}
