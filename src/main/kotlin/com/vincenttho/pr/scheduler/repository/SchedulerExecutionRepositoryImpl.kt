package com.vincenttho.pr.scheduler.repository

import com.vincenttho.pr.scheduler.entity.SchedulerExecution
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.jdbc.core.RowMapper
import org.springframework.stereotype.Repository
import java.math.BigDecimal
import java.sql.ResultSet
import java.time.LocalDateTime

/**
 * Custom implementation of SchedulerExecutionRepositoryCustom
 * Uses JdbcTemplate for legacy Oracle pagination
 * <AUTHOR>
 */
@Repository
class SchedulerExecutionRepositoryImpl(
    private val jdbcTemplate: JdbcTemplate
) : SchedulerExecutionRepositoryCustom {

    override fun findBySchedulerIdOrderByStartTimeDescWithLegacyPagination(
        schedulerId: String,
        pageable: Pageable
    ): Page<SchedulerExecution> {
        // Get today's date range (from 00:00:00 to 23:59:59)
        val today = LocalDateTime.now().toLocalDate()
        val startOfDay = today.atStartOfDay()
        val endOfDay = today.plusDays(1).atStartOfDay().minusNanos(1)

        // Use the time range method with today's date range
        return findBySchedulerIdAndTimeRangeWithLegacyPagination(schedulerId, startOfDay, endOfDay, pageable)
    }

    override fun findBySchedulerIdAndTimeRangeWithLegacyPagination(
        schedulerId: String,
        startTime: LocalDateTime,
        endTime: LocalDateTime,
        pageable: Pageable
    ): Page<SchedulerExecution> {
        // Calculate pagination parameters
        val pageSize = pageable.pageSize
        val pageNumber = pageable.pageNumber
        val startRow = pageNumber * pageSize + 1
        val endRow = startRow + pageSize - 1

        // SQL for older Oracle versions using ROWNUM for pagination with time range
        val sql = """
            SELECT * FROM (
                SELECT a.*, ROWNUM rnum FROM (
                    SELECT id, scheduler_id, start_time, end_time, success, message
                    FROM t_pr_scheduler_executions
                    WHERE scheduler_id = ?
                    AND start_time BETWEEN ? AND ?
                    ORDER BY start_time DESC
                ) a WHERE ROWNUM <= ?
            ) WHERE rnum >= ?
        """.trimIndent()

        // Execute query with pagination parameters and time range
        val results = jdbcTemplate.query(
            sql,
            SchedulerExecutionRowMapper(),
            schedulerId,
            java.sql.Timestamp.valueOf(startTime),
            java.sql.Timestamp.valueOf(endTime),
            endRow,
            startRow
        )

        // Count total elements for pagination metadata with time range
        val countSql = "SELECT COUNT(*) FROM t_pr_scheduler_executions WHERE scheduler_id = ? AND start_time BETWEEN ? AND ?"
        val totalElements = jdbcTemplate.queryForObject(
            countSql,
            Long::class.java,
            schedulerId,
            java.sql.Timestamp.valueOf(startTime),
            java.sql.Timestamp.valueOf(endTime)
        ) ?: 0L

        return PageImpl(results, pageable, totalElements)
    }

    /**
     * RowMapper for SchedulerExecution
     */
    private class SchedulerExecutionRowMapper : RowMapper<SchedulerExecution> {
        override fun mapRow(rs: ResultSet, rowNum: Int): SchedulerExecution {
            val execution = SchedulerExecution(
                id = rs.getString("id"),
                schedulerId = rs.getString("scheduler_id"),
                startTime = rs.getTimestamp("start_time").toLocalDateTime()
            )

            // Handle nullable fields
            rs.getTimestamp("end_time")?.let {
                execution.endTime = it.toLocalDateTime()
            }

            rs.getObject("success")?.let {
                execution.success = (it as BigDecimal).compareTo(BigDecimal.ONE) >= 0
            }

            execution.message = rs.getString("message")

            return execution
        }
    }
}
