package com.vincenttho.pr.scheduler

import com.vincenttho.pr.scheduler.service.SchedulerManagerService
import com.vincenttho.pr.scheduler.service.CommonSchedulerService
import jakarta.annotation.PostConstruct
import org.springframework.context.annotation.Configuration

/**
 * <p>scheduler register</p>
 * <AUTHOR>
 * @date 2025-05-09
 */
@Configuration
class SchedulerRegister(
    private val schedulerManagerService: SchedulerManagerService,
    private val schedulerService: CommonSchedulerService,
) {

    @PostConstruct
    fun register() {
        schedulerManagerService.getAllSchedulers()
            .forEach {
                schedulerManagerService.registerTask(it) {
                    schedulerService.run(it)
                }
            }
    }

}