package com.vincenttho.pr.scheduler.controller

import com.vincenttho.pr.scheduler.entity.DataSourceTypeEnum
import com.vincenttho.pr.scheduler.entity.SchedulerDsConfig
import com.vincenttho.pr.scheduler.service.SchedulerDsConfigService
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Controller
import org.springframework.ui.Model
import org.springframework.web.bind.annotation.*

/**
 * 调度器数据源配置控制器
 * <AUTHOR>
 */
@Controller
@RequestMapping("/schedulers/datasources")
class SchedulerDsConfigController(
    private val schedulerDsConfigService: SchedulerDsConfigService
) {
    
    /**
     * 数据源配置列表页面
     */
    @GetMapping
    fun list(
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "10") size: Int,
        model: Model
    ): String {
        val pageable = PageRequest.of(page, size, Sort.by("createdAt").descending())
        val datasources = schedulerDsConfigService.findAll(pageable)
        
        model.addAttribute("datasources", datasources)
        model.addAttribute("currentPage", page)
        model.addAttribute("totalPages", datasources.totalPages)
        model.addAttribute("dataSourceTypes", DataSourceTypeEnum.values())
        
        return "scheduler/datasource-list"
    }
    
    /**
     * 创建数据源配置页面
     */
    @GetMapping("/create")
    fun createForm(model: Model): String {
        model.addAttribute("datasource", SchedulerDsConfig())
        model.addAttribute("dataSourceTypes", DataSourceTypeEnum.values())
        return "scheduler/datasource-form"
    }
    
    /**
     * 编辑数据源配置页面
     */
    @GetMapping("/{id}/edit")
    fun editForm(@PathVariable id: String, model: Model): String {
        val datasource = schedulerDsConfigService.findById(id)
            ?: throw IllegalArgumentException("数据源配置不存在: $id")
        
        model.addAttribute("datasource", datasource)
        model.addAttribute("dataSourceTypes", DataSourceTypeEnum.values())
        return "scheduler/datasource-form"
    }
    
    /**
     * 保存数据源配置
     */
    @PostMapping
    fun save(@ModelAttribute datasource: SchedulerDsConfig): String {
        schedulerDsConfigService.save(datasource)
        return "redirect:/schedulers/datasources"
    }
    
    /**
     * 删除数据源配置
     */
    @DeleteMapping("/{id}")
    @ResponseBody
    fun delete(@PathVariable id: String): ResponseEntity<Map<String, Any>> {
        return try {
            schedulerDsConfigService.deleteById(id)
            ResponseEntity.ok(mapOf("success" to true, "message" to "删除成功"))
        } catch (e: Exception) {
            ResponseEntity.badRequest().body(mapOf("success" to false, "message" to (e.message ?: "Unknown error")))
        }
    }
    
    /**
     * 测试数据源连接
     */
    @PostMapping("/{id}/test")
    @ResponseBody
    fun testConnection(@PathVariable id: String): ResponseEntity<Map<String, Any>> {
        return try {
            val success = schedulerDsConfigService.testConnection(id)
            if (success) {
                ResponseEntity.ok(mapOf("success" to true, "message" to "连接测试成功"))
            } else {
                ResponseEntity.ok(mapOf("success" to false, "message" to "连接测试失败"))
            }
        } catch (e: Exception) {
            ResponseEntity.ok(mapOf("success" to false, "message" to "连接测试失败: ${e.message}"))
        }
    }
    
    /**
     * 测试数据源配置连接（不保存）
     */
    @PostMapping("/test")
    @ResponseBody
    fun testConnectionConfig(@RequestBody datasource: SchedulerDsConfig): ResponseEntity<Map<String, Any>> {
        return try {
            val success = schedulerDsConfigService.testConnection(datasource)
            if (success) {
                ResponseEntity.ok(mapOf("success" to true, "message" to "连接测试成功"))
            } else {
                ResponseEntity.ok(mapOf("success" to false, "message" to "连接测试失败"))
            }
        } catch (e: Exception) {
            ResponseEntity.ok(mapOf("success" to false, "message" to "连接测试失败: ${e.message}"))
        }
    }
    
    /**
     * 获取数据源配置详情
     */
    @GetMapping("/{id}")
    @ResponseBody
    fun getById(@PathVariable id: String): ResponseEntity<SchedulerDsConfig> {
        val datasource = schedulerDsConfigService.findById(id)
            ?: return ResponseEntity.notFound().build()
        return ResponseEntity.ok(datasource)
    }
    
    /**
     * 获取所有数据源配置（用于下拉选择）
     */
    @GetMapping("/options")
    @ResponseBody
    fun getAllOptions(): ResponseEntity<List<Map<String, String>>> {
        val datasources = schedulerDsConfigService.findAll()
        val options = datasources.map { 
            mapOf(
                "id" to it.id,
                "name" to "${it.id} (${it.dataSourceType.name})"
            )
        }
        return ResponseEntity.ok(options)
    }
}
