package com.vincenttho.pr.scheduler.controller

import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.servlet.view.RedirectView

/**
 * Home Controller to handle root path requests
 * <AUTHOR>
 */
@Controller
@RequestMapping("/")
class HomeController {

    /**
     * Redirect root path to schedulers page
     * @return RedirectView to schedulers page
     */
    @GetMapping
    fun redirectToSchedulers(): RedirectView {
        return RedirectView("/schedulers")
    }
}
