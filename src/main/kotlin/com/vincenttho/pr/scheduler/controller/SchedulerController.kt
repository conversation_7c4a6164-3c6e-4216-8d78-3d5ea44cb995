package com.vincenttho.pr.scheduler.controller

import com.vincenttho.pr.scheduler.controller.request.SchedulerCreateRequest
import com.vincenttho.pr.scheduler.entity.SchedulerConfig
import com.vincenttho.pr.scheduler.service.SchedulerManagerService
import org.slf4j.LoggerFactory
import org.springframework.data.domain.PageRequest
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Controller
import org.springframework.ui.Model
import org.springframework.web.bind.annotation.*
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID

/**
 * Controller for scheduler management
 * <AUTHOR>
 */
@Controller
@RequestMapping("\${scheduler.manager.base-path:/schedulers}")
class SchedulerController(
    private val schedulerManagerService: SchedulerManagerService,
    private val schedulerDsConfigService: SchedulerDsConfigService
) {
    private val logger = LoggerFactory.getLogger(SchedulerController::class.java)

    /**
     * Get scheduler management page
     * @param model Model
     * @return View name
     */
    @GetMapping
    fun getSchedulersPage(model: Model): String {
        model.addAttribute("schedulers", schedulerManagerService.getAllSchedulers())
        return "schedulers"
    }

    /**
     * Get scheduler details page
     * @param id Scheduler ID
     * @param timeRange Time range filter (today, last3days, lastWeek, lastMonth)
     * @param page Page number
     * @param size Page size
     * @param model Model
     * @return View name
     */
    @GetMapping("/{id}")
    fun getSchedulerDetailsPage(
        @PathVariable id: String,
        @RequestParam(required = false, defaultValue = "today") timeRange: String,
        @RequestParam(required = false, defaultValue = "0") page: Int,
        @RequestParam(required = false, defaultValue = "10") size: Int,
        model: Model
    ): String {
        val scheduler = schedulerManagerService.getSchedulerById(id)
            ?: return "redirect:/schedulers"

        model.addAttribute("scheduler", scheduler)
        model.addAttribute("currentTimeRange", timeRange)
        model.addAttribute("currentPage", page)
        model.addAttribute("pageSize", size)

        // Calculate time range
        val (startTime, endTime) = getTimeRangeForFilter(timeRange)

        // Get execution history with time range filter
        val executions = schedulerManagerService.getSchedulerExecutionsByTimeRange(
            id, startTime, endTime, PageRequest.of(page, size)
        )
        model.addAttribute("executions", executions)

        return "scheduler-details"
    }

    /**
     * Get time range for filter
     * @param timeRange Time range filter (today, last3days, lastWeek, lastMonth)
     * @return Pair of start time and end time
     */
    private fun getTimeRangeForFilter(timeRange: String): Pair<LocalDateTime, LocalDateTime> {
        val now = LocalDate.now()
        val endOfDay = LocalDateTime.of(now, LocalTime.MAX)

        val startTime = when (timeRange) {
            "last3days" -> LocalDateTime.of(now.minusDays(2), LocalTime.MIN)
            "lastWeek" -> LocalDateTime.of(now.minusDays(6), LocalTime.MIN)
            "lastMonth" -> LocalDateTime.of(now.minusDays(29), LocalTime.MIN)
            else -> LocalDateTime.of(now, LocalTime.MIN) // Default to today
        }

        return Pair(startTime, endOfDay)
    }

    /**
     * Update scheduler
     * @param id Scheduler ID
     * @param name Scheduler name
     * @param cronExpression Cron expression
     * @param sql SQL query to execute
     * @param wxApiKey WeChat API key
     * @return Redirect URL
     */
    @PostMapping("/{id}/update")
    fun updateScheduler(
        @PathVariable id: String,
        @RequestParam name: String,
        @RequestParam cronExpression: String,
        @RequestParam sql: String,
        @RequestParam wxApiKey: String,
        @RequestParam description: String?
    ): String {
        val scheduler = schedulerManagerService.getSchedulerById(id)
            ?: return "redirect:/schedulers"

        scheduler.cronExpression = cronExpression
        scheduler.name = name
        scheduler.sql = sql
        scheduler.wxApiKey = wxApiKey
        scheduler.description = description
        schedulerManagerService.updateScheduler(scheduler)

        return "redirect:/schedulers"
    }

    /**
     * Start scheduler
     * @param id Scheduler ID
     * @return Redirect URL
     */
    @PostMapping("/{id}/start")
    fun startScheduler(@PathVariable id: String): String {
        schedulerManagerService.startScheduler(id)
        return "redirect:/schedulers"
    }

    /**
     * Pause scheduler
     * @param id Scheduler ID
     * @return Redirect URL
     */
    @PostMapping("/{id}/pause")
    fun pauseScheduler(@PathVariable id: String): String {
        schedulerManagerService.pauseScheduler(id)
        return "redirect:/schedulers"
    }

    /**
     * Run scheduler now
     * @param id Scheduler ID
     * @return Redirect URL
     */
    @PostMapping("/{id}/run")
    fun runSchedulerNow(@PathVariable id: String): String {
        try {
            schedulerManagerService.runSchedulerNow(id)
        } catch (e: Exception) {
            // 捕获异常但不中断流程，异常信息已经被记录到执行历史中
            // 日志中记录异常信息，但不影响界面重定向
            logger.error("Error running scheduler {}: {}", id, e.message, e)
        }
        return "redirect:/schedulers/${id}"
    }

    /**
     * Delete scheduler
     * @param id Scheduler ID
     * @return Redirect URL
     */
    @PostMapping("/{id}/delete")
    fun deleteScheduler(@PathVariable id: String): String {
        try {
            schedulerManagerService.deleteScheduler(id)
            logger.info("Scheduler deleted: {}", id)
        } catch (e: Exception) {
            logger.error("Error deleting scheduler {}: {}", id, e.message, e)
        }
        return "redirect:/schedulers"
    }

    /**
     * Create new scheduler
     * @param name Scheduler name
     * @param description Scheduler description (optional)
     * @param cronExpression Cron expression
     * @param wxApiKey WeChat API key
     * @param sql SQL query to execute
     * @return Redirect URL
     */
    @PostMapping("/create")
    fun createScheduler(
        @RequestParam name: String,
        @RequestParam(required = false, defaultValue = "") description: String,
        @RequestParam cronExpression: String,
        @RequestParam wxApiKey: String,
        @RequestParam sql: String
    ): String {
        val config = SchedulerConfig(
            id = UUID.randomUUID().toString(),
            name = name,
            description = description,
            cronExpression = cronExpression,
            enabled = true,
            wxApiKey = wxApiKey,
            sql = sql
        )

        schedulerManagerService.createScheduler(config)

        return "redirect:/schedulers"
    }

    /**
     * REST API endpoints for AJAX calls
     */
    @RestController
    @RequestMapping("\${scheduler.manager.base-path:/schedulers}/api")
    class SchedulerRestController(private val schedulerManagerService: SchedulerManagerService) {
        private val logger = LoggerFactory.getLogger(SchedulerRestController::class.java)

        /**
         * Get all schedulers
         * @return List of scheduler configurations
         */
        @GetMapping
        fun getAllSchedulers(): List<SchedulerConfig> {
            return schedulerManagerService.getAllSchedulers()
        }

        /**
         * Get scheduler by ID
         * @param id Scheduler ID
         * @return Scheduler configuration
         */
        @GetMapping("/{id}")
        fun getSchedulerById(@PathVariable id: String): ResponseEntity<SchedulerConfig> {
            val scheduler = schedulerManagerService.getSchedulerById(id)
                ?: return ResponseEntity.notFound().build()

            return ResponseEntity.ok(scheduler)
        }

        /**
         * Update scheduler
         * @param id Scheduler ID
         * @param config Scheduler configuration
         * @return Updated scheduler configuration
         */
        @PutMapping("/{id}")
        fun updateScheduler(
            @PathVariable id: String,
            @RequestBody config: SchedulerConfig
        ): ResponseEntity<SchedulerConfig> {
            if (id != config.id) {
                return ResponseEntity.badRequest().build()
            }

            return ResponseEntity.ok(schedulerManagerService.updateScheduler(config))
        }

        /**
         * Start scheduler
         * @param id Scheduler ID
         * @return Updated scheduler configuration
         */
        @PostMapping("/{id}/start")
        fun startScheduler(@PathVariable id: String): ResponseEntity<SchedulerConfig> {
            return ResponseEntity.ok(schedulerManagerService.startScheduler(id))
        }

        /**
         * Pause scheduler
         * @param id Scheduler ID
         * @return Updated scheduler configuration
         */
        @PostMapping("/{id}/pause")
        fun pauseScheduler(@PathVariable id: String): ResponseEntity<SchedulerConfig> {
            return ResponseEntity.ok(schedulerManagerService.pauseScheduler(id))
        }

        /**
         * Run scheduler now
         * @param id Scheduler ID
         * @return Success message
         */
        @PostMapping("/{id}/run")
        fun runSchedulerNow(@PathVariable id: String): ResponseEntity<Map<String, String>> {
            try {
                schedulerManagerService.runSchedulerNow(id)
                return ResponseEntity.ok(mapOf("message" to "Scheduler executed successfully"))
            } catch (e: Exception) {
                // 捕获异常但不中断流程，异常信息已经被记录到执行历史中
                logger.error("Error running scheduler {}: {}", id, e.message, e)
                return ResponseEntity.ok(mapOf("message" to "Scheduler execution failed, but the error has been logged. Check execution history for details."))
            }
        }

        /**
         * Delete scheduler
         * @param id Scheduler ID
         * @return Success message
         */
        @DeleteMapping("/{id}")
        fun deleteScheduler(@PathVariable id: String): ResponseEntity<Map<String, String>> {
            try {
                schedulerManagerService.deleteScheduler(id)
                logger.info("Scheduler deleted: {}", id)
                return ResponseEntity.ok(mapOf("message" to "Scheduler deleted successfully"))
            } catch (e: Exception) {
                logger.error("Error deleting scheduler {}: {}", id, e.message, e)
                return ResponseEntity.status(500).body(mapOf("message" to "Error deleting scheduler: ${e.message}"))
            }
        }
    }
}
