package com.vincenttho.pr.scheduler.config

import org.springframework.boot.context.properties.ConfigurationProperties

/**
 * Configuration properties for ScheduleHub
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "schedule.hub")
data class ScheduleHubProperties(
    /**
     * Whether the ScheduleHub is enabled
     */
    val enabled: Boolean = true,

    /**
     * Base path for the ScheduleHub web interface
     */
    val basePath: String = "/schedulers",

    /**
     * Thread pool size for the scheduler
     */
    val threadPoolSize: Int = 5,

    /**
     * Whether to enable the REST API
     */
    val enableRestApi: Boolean = true,

    /**
     * Storage type: "json" or "database"
     */
    val storageType: String = "json",

    /**
     * Path for JSON storage files
     */
    val storagePath: String = "./schedule-hub-data"
)
