package com.vincenttho.pr.scheduler.autoconfigure

import com.vincenttho.pr.scheduler.config.ScheduleHubProperties
import com.vincenttho.pr.scheduler.controller.SchedulerController
import com.vincenttho.pr.scheduler.repository.SchedulerConfigRepository
import com.vincenttho.pr.scheduler.repository.SchedulerExecutionRepository
import com.vincenttho.pr.scheduler.service.SchedulerManagerService
import com.vincenttho.pr.scheduler.storage.JpaStorageProvider
import com.vincenttho.pr.scheduler.storage.JsonFileStorageProvider
import com.vincenttho.pr.scheduler.storage.SchedulerStorageProvider
import org.springframework.boot.autoconfigure.AutoConfiguration
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication
import org.springframework.boot.autoconfigure.domain.EntityScan
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.Configuration
import org.springframework.data.jpa.repository.config.EnableJpaRepositories
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.scheduling.TaskScheduler
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler
import javax.sql.DataSource

/**
 * ScheduleHub Auto Configuration
 * <AUTHOR>
 */
@AutoConfiguration
@EnableConfigurationProperties(ScheduleHubProperties::class)
@ConditionalOnProperty(prefix = "schedule.hub", name = ["enabled"], havingValue = "true", matchIfMissing = true)
@ComponentScan(basePackages = ["com.vincenttho.scheduler"])
class ScheduleHubAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    fun taskScheduler(properties: ScheduleHubProperties): TaskScheduler {
        val scheduler = ThreadPoolTaskScheduler()
        scheduler.poolSize = properties.threadPoolSize
        scheduler.setThreadNamePrefix("schedule-hub-")
        scheduler.setWaitForTasksToCompleteOnShutdown(true)
        scheduler.setAwaitTerminationSeconds(60)
        return scheduler
    }

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "schedule.hub", name = ["storage-type"], havingValue = "json", matchIfMissing = true)
    fun jsonStorageProvider(properties: ScheduleHubProperties): SchedulerStorageProvider {
        return JsonFileStorageProvider(properties)
    }

    @Configuration
    @ConditionalOnProperty(prefix = "schedule.hub", name = ["storage-type"], havingValue = "database")
    @EntityScan(basePackages = ["com.vincenttho.pr.scheduler.entity"])
    @EnableJpaRepositories(basePackages = ["com.vincenttho.pr.scheduler.repository"])
    class DatabaseStorageConfiguration {

        @Bean
        @ConditionalOnMissingBean
        fun jdbcTemplate(dataSource: DataSource): JdbcTemplate {
            return JdbcTemplate(dataSource)
        }

        @Bean
        @ConditionalOnMissingBean
        fun jpaStorageProvider(
            schedulerConfigRepository: SchedulerConfigRepository,
            schedulerExecutionRepository: SchedulerExecutionRepository
        ): SchedulerStorageProvider {
            return JpaStorageProvider(schedulerConfigRepository, schedulerExecutionRepository)
        }
    }

    @Configuration
    @ConditionalOnWebApplication
    class ScheduleHubWebConfiguration {

        @Bean
        fun schedulerController(schedulerManagerService: SchedulerManagerService): SchedulerController {
            return SchedulerController(schedulerManagerService)
        }
    }
}
