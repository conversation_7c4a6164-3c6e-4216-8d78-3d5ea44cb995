package com.vincenttho.pr.repository

import com.vincenttho.pr.dao.SchedulerSysConfigDao
import com.vincenttho.pr.enums.SysConfigEnum
import com.vincenttho.pr.po.SchedulerSysConfigPO
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * <p>门诊患者提醒仓储层</p>
 * <AUTHOR>
 * @date 2025-05-09
 */
@Service
class SchedulerSysConfigRepository(private val sysConfigDao: SchedulerSysConfigDao) {
    fun findValue(sysConfigEnum: SysConfigEnum): String? {
        return sysConfigDao.findById(sysConfigEnum.name).orElse(null)?.value
            ?: sysConfigEnum.defaultValue
    }

    fun save(sysConfigEnum: SysConfigEnum, value: String) {
        val sysConfig = sysConfigDao.findById(sysConfigEnum.name).orElse(null)
            ?: SchedulerSysConfigPO().apply {
                key = sysConfigEnum.name
            }
        sysConfig.value = value
        sysConfigDao.save(sysConfig)
    }

    /**
     * 查找 Scheduler Start Time
     * <AUTHOR>
     * @date 2025/05/09
     * @param [sysConfigEnum] sys config 枚举
     * @return [LocalDateTime]
     */
    fun findSchedulerStartTime(sysConfigEnum: SysConfigEnum): LocalDateTime {
        // 尝试从配置中获取开始时间
        var startTime = this.findValue(sysConfigEnum)?.let {
            // 将 yyyy-MM-dd HH:mm:ss 的字符串格式转换成 LocalDateTime
            LocalDateTime.parse(it, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
        }

        if(startTime == null) {
            // 如果没有配置，默认使用当前时间减5分钟
            startTime = LocalDateTime.now().plusMinutes(-5L)
            // 将 time 变量转换成 yyyy-MM-dd HH:mm:ss 的字符串格式
            val timeStr = startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))

            // 保存结果
            this.save(sysConfigEnum, timeStr)
        }

        return startTime!!
    }

}