package com.vincentho.pr

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.domain.EntityScan
import org.springframework.boot.runApplication
import org.springframework.data.jpa.repository.config.EnableJpaRepositories
import org.springframework.scheduling.annotation.EnableScheduling

/**
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = ["com.vincenttho"])
@EnableScheduling
@EnableJpaRepositories(basePackages = ["com.vincenttho.pr.dao"])
@EntityScan(basePackages = ["com.vincenttho.pr.po"])
class MainApplication

fun main(args: Array<String>) {
    runApplication<MainApplication>(*args)
}
