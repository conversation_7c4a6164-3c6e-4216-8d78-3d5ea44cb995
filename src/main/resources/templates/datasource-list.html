<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      th:replace="layout/main :: layout(~{::title}, ~{::#content}, 'datasources')">
<head>
    <title>数据源管理</title>
</head>
<body>
    <div id="content">
        <!-- 页面标题和操作按钮 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="page-title">
                    数据源管理
                </h1>
                <p class="text-muted">管理调度器使用的数据源配置</p>
            </div>
            <div>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createDataSourceModal">
                    <i class="bi bi-plus"></i> 创建数据源
                </button>
            </div>
        </div>

        <!-- 状态概览卡片 -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">总数据源数</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" th:text="${datasources.totalElements}">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-database fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Oracle数据源</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" 
                                     th:text="${#lists.size(#lists.select(datasources.content, 'dataSourceType.name() == \'ORACLE\''))}">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-server fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">MySQL数据源</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" 
                                     th:text="${#lists.size(#lists.select(datasources.content, 'dataSourceType.name() == \'MYSQL\''))}">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-hdd fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">SQL Server数据源</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" 
                                     th:text="${#lists.size(#lists.select(datasources.content, 'dataSourceType.name() == \'SQL_SERVER\''))}">0</div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-pc-display fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据源列表 -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">数据源列表</h6>
            </div>
            <div class="card-body">
                <div th:if="${datasources.empty}" class="text-center py-5">
                    <i class="bi bi-database" style="font-size: 3rem; color: #ccc;"></i>
                    <p class="text-muted mt-3">暂无数据源配置</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createDataSourceModal">
                        创建第一个数据源
                    </button>
                </div>

                <div th:unless="${datasources.empty}" class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>类型</th>
                                <th>主机</th>
                                <th>端口</th>
                                <th>实例</th>
                                <th>用户名</th>
                                <th>描述</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="datasource : ${datasources.content}">
                                <td>
                                    <span class="fw-bold text-primary" th:text="${datasource.id}"></span>
                                </td>
                                <td>
                                    <span class="badge" 
                                          th:classappend="${datasource.dataSourceType.name() == 'ORACLE' ? 'bg-success' : 
                                                          (datasource.dataSourceType.name() == 'MYSQL' ? 'bg-info' : 'bg-warning')}"
                                          th:text="${datasource.dataSourceType.name()}"></span>
                                </td>
                                <td th:text="${datasource.dataSourceHost}"></td>
                                <td th:text="${datasource.dataSourcePort}"></td>
                                <td th:text="${datasource.dataSourceInstance}"></td>
                                <td th:text="${datasource.dataSourceUser}"></td>
                                <td>
                                    <span th:text="${datasource.description}" class="text-muted"></span>
                                </td>
                                <td>
                                    <span th:text="${#temporals.format(datasource.createdAt, 'yyyy-MM-dd HH:mm')}" class="text-muted"></span>
                                </td>
                                <td>
                                    <div class="d-flex">
                                        <button type="button" class="btn btn-sm btn-outline-info me-1"
                                                th:onclick="'testConnection(\'' + ${datasource.id} + '\')'">
                                            <i class="bi bi-wifi"></i> 测试
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary me-1"
                                                th:onclick="'editDataSource(\'' + ${datasource.id} + '\')'">
                                            <i class="bi bi-pencil"></i> 编辑
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                th:onclick="'deleteDataSource(\'' + ${datasource.id} + '\')'">
                                            <i class="bi bi-trash"></i> 删除
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div th:if="${datasources.totalPages > 1}" class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        <span class="text-muted">
                            显示第 <span th:text="${datasources.number * datasources.size + 1}"></span> 到 
                            <span th:text="${datasources.number * datasources.size + datasources.numberOfElements}"></span> 条，
                            共 <span th:text="${datasources.totalElements}"></span> 条记录
                        </span>
                    </div>
                    <nav>
                        <ul class="pagination mb-0">
                            <li class="page-item" th:classappend="${datasources.first ? 'disabled' : ''}">
                                <a class="page-link" th:href="@{/schedulers/datasources(page=${datasources.number - 1})}">上一页</a>
                            </li>
                            <li class="page-item" th:each="i : ${#numbers.sequence(0, datasources.totalPages - 1)}"
                                th:classappend="${i == datasources.number ? 'active' : ''}">
                                <a class="page-link" th:href="@{/schedulers/datasources(page=${i})}" th:text="${i + 1}"></a>
                            </li>
                            <li class="page-item" th:classappend="${datasources.last ? 'disabled' : ''}">
                                <a class="page-link" th:href="@{/schedulers/datasources(page=${datasources.number + 1})}">下一页</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>

        <!-- 创建数据源模态框 -->
        <div class="modal fade" id="createDataSourceModal" tabindex="-1" aria-labelledby="createDataSourceModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="createDataSourceModalLabel">创建数据源</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form id="createDataSourceForm" th:action="@{/schedulers/datasources}" method="post">
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="createId" class="form-label">数据源ID <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="createId" name="id" required>
                                    <div class="form-text">唯一标识符，只能包含字母、数字和下划线</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="createType" class="form-label">数据库类型 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="createType" name="dataSourceType" required>
                                        <option value="">请选择数据库类型</option>
                                        <option th:each="type : ${dataSourceTypes}" th:value="${type.name()}" th:text="${type.name()}"></option>
                                    </select>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="createHost" class="form-label">主机地址 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="createHost" name="dataSourceHost" required>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="createPort" class="form-label">端口 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="createPort" name="dataSourcePort" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="createInstance" class="form-label">数据库实例 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="createInstance" name="dataSourceInstance" required>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="createUser" class="form-label">用户名 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="createUser" name="dataSourceUser" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="createPassword" class="form-label">密码 <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control" id="createPassword" name="dataSourcePassword" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="createDescription" class="form-label">描述</label>
                                <textarea class="form-control" id="createDescription" name="description" rows="2"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary" onclick="testCreateConnection()">
                                <i class="bi bi-wifi"></i> 测试连接
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="submit" class="btn btn-primary">保存</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 编辑数据源模态框 -->
        <div class="modal fade" id="editDataSourceModal" tabindex="-1" aria-labelledby="editDataSourceModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="editDataSourceModalLabel">编辑数据源</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form id="editDataSourceForm">
                        <div class="modal-body">
                            <input type="hidden" id="editId" name="id">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="editIdDisplay" class="form-label">数据源ID</label>
                                    <input type="text" class="form-control" id="editIdDisplay" readonly>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="editType" class="form-label">数据库类型 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="editType" name="dataSourceType" required>
                                        <option value="">请选择数据库类型</option>
                                        <option th:each="type : ${dataSourceTypes}" th:value="${type.name()}" th:text="${type.name()}"></option>
                                    </select>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="editHost" class="form-label">主机地址 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editHost" name="dataSourceHost" required>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="editPort" class="form-label">端口 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editPort" name="dataSourcePort" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="editInstance" class="form-label">数据库实例 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="editInstance" name="dataSourceInstance" required>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="editUser" class="form-label">用户名 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editUser" name="dataSourceUser" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="editPassword" class="form-label">密码 <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control" id="editPassword" name="dataSourcePassword" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="editDescription" class="form-label">描述</label>
                                <textarea class="form-control" id="editDescription" name="description" rows="2"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary" onclick="testEditConnection()">
                                <i class="bi bi-wifi"></i> 测试连接
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="submit" class="btn btn-primary">保存更改</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 测试连接
        async function testConnection(id) {
            const btn = event.target.closest('button');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> 测试中...';
            btn.disabled = true;

            try {
                const response = await fetch(`/schedulers/datasources/${id}/test`, {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (result.success) {
                    showAlert('连接测试成功', 'success');
                } else {
                    showAlert(result.message || '连接测试失败', 'danger');
                }
            } catch (error) {
                console.error('测试连接失败:', error);
                showAlert('测试连接失败', 'danger');
            } finally {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        }

        // 编辑数据源
        async function editDataSource(id) {
            try {
                const response = await fetch(`/schedulers/datasources/${id}`);
                const datasource = await response.json();
                
                document.getElementById('editId').value = datasource.id;
                document.getElementById('editIdDisplay').value = datasource.id;
                document.getElementById('editType').value = datasource.dataSourceType;
                document.getElementById('editHost').value = datasource.dataSourceHost;
                document.getElementById('editPort').value = datasource.dataSourcePort;
                document.getElementById('editInstance').value = datasource.dataSourceInstance;
                document.getElementById('editUser').value = datasource.dataSourceUser;
                document.getElementById('editPassword').value = datasource.dataSourcePassword;
                document.getElementById('editDescription').value = datasource.description || '';
                
                const modal = new bootstrap.Modal(document.getElementById('editDataSourceModal'));
                modal.show();
            } catch (error) {
                console.error('加载数据源信息失败:', error);
                showAlert('加载数据源信息失败', 'danger');
            }
        }

        // 删除数据源
        async function deleteDataSource(id) {
            if (!confirm('确定要删除这个数据源吗？此操作不可恢复。')) {
                return;
            }

            try {
                const response = await fetch(`/schedulers/datasources/${id}`, {
                    method: 'DELETE'
                });
                const result = await response.json();
                
                if (result.success) {
                    showAlert('删除成功', 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    showAlert(result.message || '删除失败', 'danger');
                }
            } catch (error) {
                console.error('删除失败:', error);
                showAlert('删除失败', 'danger');
            }
        }

        // 测试创建表单的连接
        async function testCreateConnection() {
            const form = document.getElementById('createDataSourceForm');
            const formData = new FormData(form);
            
            const data = {
                id: formData.get('id'),
                dataSourceType: formData.get('dataSourceType'),
                dataSourceHost: formData.get('dataSourceHost'),
                dataSourcePort: formData.get('dataSourcePort'),
                dataSourceInstance: formData.get('dataSourceInstance'),
                dataSourceUser: formData.get('dataSourceUser'),
                dataSourcePassword: formData.get('dataSourcePassword'),
                description: formData.get('description')
            };

            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> 测试中...';
            btn.disabled = true;

            try {
                const response = await fetch('/schedulers/datasources/test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                
                if (result.success) {
                    showAlert('连接测试成功', 'success');
                } else {
                    showAlert(result.message || '连接测试失败', 'danger');
                }
            } catch (error) {
                console.error('测试连接失败:', error);
                showAlert('测试连接失败', 'danger');
            } finally {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        }

        // 测试编辑表单的连接
        async function testEditConnection() {
            const form = document.getElementById('editDataSourceForm');
            const formData = new FormData(form);
            
            const data = {
                id: formData.get('id'),
                dataSourceType: formData.get('dataSourceType'),
                dataSourceHost: formData.get('dataSourceHost'),
                dataSourcePort: formData.get('dataSourcePort'),
                dataSourceInstance: formData.get('dataSourceInstance'),
                dataSourceUser: formData.get('dataSourceUser'),
                dataSourcePassword: formData.get('dataSourcePassword'),
                description: formData.get('description')
            };

            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="bi bi-hourglass-split"></i> 测试中...';
            btn.disabled = true;

            try {
                const response = await fetch('/schedulers/datasources/test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                
                if (result.success) {
                    showAlert('连接测试成功', 'success');
                } else {
                    showAlert(result.message || '连接测试失败', 'danger');
                }
            } catch (error) {
                console.error('测试连接失败:', error);
                showAlert('测试连接失败', 'danger');
            } finally {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        }

        // 处理编辑表单提交
        document.getElementById('editDataSourceForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {
                id: formData.get('id'),
                dataSourceType: formData.get('dataSourceType'),
                dataSourceHost: formData.get('dataSourceHost'),
                dataSourcePort: formData.get('dataSourcePort'),
                dataSourceInstance: formData.get('dataSourceInstance'),
                dataSourceUser: formData.get('dataSourceUser'),
                dataSourcePassword: formData.get('dataSourcePassword'),
                description: formData.get('description')
            };

            try {
                const response = await fetch('/schedulers/datasources', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                if (response.ok) {
                    showAlert('保存成功', 'success');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('editDataSourceModal'));
                    modal.hide();
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    const errorData = await response.json();
                    showAlert(errorData.message || '保存失败', 'danger');
                }
            } catch (error) {
                console.error('保存失败:', error);
                showAlert('保存失败', 'danger');
            }
        });

        // 显示提示信息
        function showAlert(message, type = 'info') {
            // 移除现有的提示
            const existingAlert = document.querySelector('.alert-floating');
            if (existingAlert) {
                existingAlert.remove();
            }

            // 创建新的提示
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show alert-floating`;
            alert.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
            `;
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alert);

            // 3秒后自动消失
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>
