<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调度器详情</title>
    <link href="/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/fonts/bootstrap-icons.css">
    <link href="/fonts/googleapis-css.css" rel="stylesheet">
    <link href="/css/scheduler-manager.css" rel="stylesheet">
</head>
<body class="fade-in">
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: var(--gradient-primary);">
        <div class="container">
            <a class="navbar-brand" href="/schedulers">
                调度器管理系统
            </a>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="page-title">
                    <a href="/schedulers" class="btn btn-outline-primary me-2">
                        返回首页
                    </a>
                    调度器详情
                </h1>
                <p class="text-muted" th:text="${scheduler.description}"></p>
            </div>
            <div>
                <form th:if="${scheduler.enabled}" th:action="@{/schedulers/{id}/pause(id=${scheduler.id})}" method="post" class="d-inline">
                    <button type="submit" class="btn btn-warning">
                        暂停
                    </button>
                </form>
                <form th:unless="${scheduler.enabled}" th:action="@{/schedulers/{id}/start(id=${scheduler.id})}" method="post" class="d-inline">
                    <button type="submit" class="btn btn-success">
                        启动
                    </button>
                </form>
                <form th:action="@{/schedulers/{id}/run(id=${scheduler.id})}" method="post" class="d-inline">
                    <button type="submit" class="btn btn-primary">
                        立即执行
                    </button>
                </form>
                <button type="button" class="btn btn-secondary" data-bs-toggle="modal" data-bs-target="#editSchedulerModal">
                    编辑
                </button>
            </div>
        </div>

        <!-- 调度器状态概览 -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">状态</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <span class="scheduler-status" th:classappend="${scheduler.enabled ? 'active' : 'inactive'}"></span>
                                    <span th:text="${scheduler.enabled ? '活动' : '停用'}"
                                          th:class="${scheduler.enabled ? 'text-success' : 'text-danger'}"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">名称</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <span th:text="${scheduler.name}"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">上次执行</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <span th:if="${scheduler.lastExecutionTime != null}" th:text="${#temporals.format(scheduler.lastExecutionTime, 'HH:mm:ss')}"></span>
                                    <span th:unless="${scheduler.lastExecutionTime != null}" class="text-muted">从未执行</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">下次执行</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <span th:if="${scheduler.nextExecutionTime != null}" th:text="${#temporals.format(scheduler.nextExecutionTime, 'HH:mm:ss')}"></span>
                                    <span th:unless="${scheduler.nextExecutionTime != null}" class="text-muted">无</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 调度器信息卡片 -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0">调度器信息</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <span class="scheduler-status" th:classappend="${scheduler.enabled ? 'active' : 'inactive'}"
                                  th:title="${scheduler.enabled ? 'Active' : 'Inactive'}"></span>
                            <span th:text="${scheduler.enabled ? '活动' : '停用'}"></span>
                        </div>
                        <div class="mb-3">
                            <strong>标识符:</strong> <span th:text="${scheduler.id}"></span>
                        </div>
                        <div class="mb-3">
                            <strong>名称:</strong> <span th:text="${scheduler.name}"></span>
                        </div>
                        <div class="mb-3">
                            <strong>描述:</strong> <span th:text="${scheduler.description}"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <strong>Cron 表达式:</strong> <code th:text="${scheduler.cronExpression}"></code>
                        </div>
                        <div class="mb-3">
                            <strong>上次执行:</strong> <span th:text="${scheduler.lastExecutionTime != null ? #temporals.format(scheduler.lastExecutionTime, 'yyyy-MM-dd HH:mm:ss') : '从未执行'}"></span>
                        </div>
                        <div class="mb-3">
                            <strong>下次执行:</strong> <span th:text="${scheduler.nextExecutionTime != null ? #temporals.format(scheduler.nextExecutionTime, 'yyyy-MM-dd HH:mm:ss') : '无'}"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 执行历史卡片 -->
        <div class="card shadow">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">执行历史</h5>
                <div class="btn-group time-filter-group">
                    <a th:href="@{/schedulers/{id}(id=${scheduler.id}, timeRange='today')}"
                       class="btn btn-sm" th:classappend="${currentTimeRange == 'today' ? 'btn-primary' : 'btn-outline-primary'}">今天</a>
                    <a th:href="@{/schedulers/{id}(id=${scheduler.id}, timeRange='last3days')}"
                       class="btn btn-sm" th:classappend="${currentTimeRange == 'last3days' ? 'btn-primary' : 'btn-outline-primary'}">近三天</a>
                    <a th:href="@{/schedulers/{id}(id=${scheduler.id}, timeRange='lastWeek')}"
                       class="btn btn-sm" th:classappend="${currentTimeRange == 'lastWeek' ? 'btn-primary' : 'btn-outline-primary'}">近一周</a>
                    <a th:href="@{/schedulers/{id}(id=${scheduler.id}, timeRange='lastMonth')}"
                       class="btn btn-sm" th:classappend="${currentTimeRange == 'lastMonth' ? 'btn-primary' : 'btn-outline-primary'}">近一个月</a>
                </div>
            </div>
            <div class="card-body p-0">
                <div th:if="${executions.empty}" class="alert alert-info m-3">
                    没有找到执行历史。
                </div>

                <div th:unless="${executions.empty}" class="execution-history-container">
                    <div class="execution-table-container" style="max-height: 500px; overflow-y: auto;">
                        <table class="table table-striped table-hover mb-0">
                            <thead class="sticky-top bg-white">
                                <tr>
                                    <th>开始时间</th>
                                    <th>结束时间</th>
                                    <th>状态</th>
                                    <th>消息</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr th:each="execution : ${executions}">
                                    <td>
                                        <div>
                                            <span class="text-dark fw-bold" th:text="${#temporals.format(execution.startTime, 'yyyy-MM-dd')}"></span><br>
                                            <small class="text-muted" th:text="${#temporals.format(execution.startTime, 'HH:mm:ss')}"></small>
                                        </div>
                                    </td>
                                    <td>
                                        <div th:if="${execution.endTime != null}">
                                            <span class="text-dark fw-bold" th:text="${#temporals.format(execution.endTime, 'yyyy-MM-dd')}"></span><br>
                                            <small class="text-muted" th:text="${#temporals.format(execution.endTime, 'HH:mm:ss')}"></small>
                                        </div>
                                        <span th:unless="${execution.endTime != null}" class="text-primary">
                                            正在运行...
                                        </span>
                                    </td>
                                    <td>
                                        <span th:if="${execution.success == null}" class="badge bg-info">
                                            运行中
                                        </span>
                                        <span th:if="${execution.success == true}" class="badge bg-success">
                                            成功
                                        </span>
                                        <span th:if="${execution.success == false}" class="badge bg-danger">
                                            失败
                                        </span>
                                    </td>
                                    <td class="message-cell">
                                        <span th:text="${execution.message}"></span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination Controls -->
                    <div class="pagination-container d-flex justify-content-between align-items-center p-3 bg-white border-top">

                        <div>
                            <span>每页显示: </span>
                            <div class="btn-group">
                                <a th:href="@{/schedulers/{id}(id=${scheduler.id}, timeRange=${currentTimeRange}, size=10, page=${currentPage})}"
                                   class="btn btn-sm" th:classappend="${pageSize == 10 ? 'btn-secondary' : 'btn-outline-secondary'}">10</a>
                                <a th:href="@{/schedulers/{id}(id=${scheduler.id}, timeRange=${currentTimeRange}, size=20, page=${currentPage})}"
                                   class="btn btn-sm" th:classappend="${pageSize == 20 ? 'btn-secondary' : 'btn-outline-secondary'}">20</a>
                                <a th:href="@{/schedulers/{id}(id=${scheduler.id}, timeRange=${currentTimeRange}, size=50, page=${currentPage})}"
                                   class="btn btn-sm" th:classappend="${pageSize == 50 ? 'btn-secondary' : 'btn-outline-secondary'}">50</a>
                                <a th:href="@{/schedulers/{id}(id=${scheduler.id}, timeRange=${currentTimeRange}, size=100, page=${currentPage})}"
                                   class="btn btn-sm" th:classappend="${pageSize == 100 ? 'btn-secondary' : 'btn-outline-secondary'}">100</a>
                            </div>
                        </div>

                        <nav aria-label="Page navigation">
                            <ul class="pagination pagination-sm mb-0">
                                <li class="page-item" th:classappend="${executions.first ? 'disabled' : ''}">
                                    <a class="page-link" th:href="@{/schedulers/{id}(id=${scheduler.id}, timeRange=${currentTimeRange}, size=${pageSize}, page=0)}">首页</a>
                                </li>
                                <li class="page-item" th:classappend="${executions.first ? 'disabled' : ''}">
                                    <a class="page-link" th:href="@{/schedulers/{id}(id=${scheduler.id}, timeRange=${currentTimeRange}, size=${pageSize}, page=${executions.number - 1})}">上一页</a>
                                </li>
                                <li class="page-item disabled">
                                    <span class="page-link" th:text="${executions.number + 1} + ' / ' + ${executions.totalPages}"></span>
                                </li>
                                <li class="page-item" th:classappend="${executions.last ? 'disabled' : ''}">
                                    <a class="page-link" th:href="@{/schedulers/{id}(id=${scheduler.id}, timeRange=${currentTimeRange}, size=${pageSize}, page=${executions.number + 1})}">下一页</a>
                                </li>
                                <li class="page-item" th:classappend="${executions.last ? 'disabled' : ''}">
                                    <a class="page-link" th:href="@{/schedulers/{id}(id=${scheduler.id}, timeRange=${currentTimeRange}, size=${pageSize}, page=${executions.totalPages - 1})}">尾页</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Scheduler Modal -->
    <div class="modal fade" id="editSchedulerModal" tabindex="-1" aria-labelledby="editSchedulerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editSchedulerModalLabel">编辑调度器</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form th:action="@{/schedulers/{id}/update(id=${scheduler.id})}" method="post">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="name" class="form-label">名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name"
                                   th:value="${scheduler.name}" required>
                            <div class="form-text">调度器的名称，用于显示和识别</div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">描述</label>
                            <textarea class="form-control" id="description" name="description" rows="2"
                                      th:text="${scheduler.description}"></textarea>
                            <div class="form-text">可选：调度器的详细描述</div>
                        </div>

                        <div class="mb-3">
                            <label for="cronExpression" class="form-label">Cron 表达式 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control form-control-lg font-monospace" id="cronExpression" name="cronExpression"
                                   th:value="${scheduler.cronExpression}" style="font-size: 1.1rem;" required>
                            <div class="form-text">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>格式:</strong> <code>秒 分 时 日 月 周</code>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>常用示例:</strong>
                                        <ul class="mb-0">
                                            <li><code>0 0 8 * * ?</code> - 每天上午8点</li>
                                            <li><code>0 0/30 * * * ?</code> - 每30分钟</li>
                                            <li><code>0 0/5 * * * ?</code> - 每5分钟</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="wxApiKey" class="form-label">微信API密钥 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="wxApiKey" name="wxApiKey"
                                   th:value="${scheduler.wxApiKey}" required>
                            <div class="form-text">用于发送微信消息的API密钥</div>
                        </div>

                        <div class="mb-4">
                            <label for="sql" class="form-label">SQL查询 <span class="text-danger">*</span></label>
                            <textarea class="form-control font-monospace" id="sql" name="sql" rows="15"
                                      style="font-size: 1rem; line-height: 1.5;" required th:text="${scheduler.sql}"></textarea>
                            <div class="form-text">调度器执行的SQL查询语句，可以包含复杂的查询逻辑</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            取消
                        </button>
                        <button type="submit" class="btn btn-primary">
                            保存更改
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="/js/bootstrap.bundle.min.js"></script>
    <script src="/js/scheduler-details.js"></script>
</body>
</html>
