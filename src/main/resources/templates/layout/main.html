<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${pageTitle ?: 'ScheduleHub 调度管理系统'}">ScheduleHub 调度管理系统</title>
    <link href="/css/bootstrap.min.css" rel="stylesheet">
    <link href="/fonts/googleapis-css.css" rel="stylesheet">
    <link href="/fonts/bootstrap-icons.css" rel="stylesheet">
    <link href="/css/schedule-hub.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 4px 8px;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover {
            color: white;
            background: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.2);
            font-weight: 600;
        }
        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        .main-content {
            background: #f8f9fa;
            min-height: 100vh;
        }
        .navbar-brand {
            font-weight: 600;
            font-size: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 左侧菜单栏 -->
            <nav class="col-md-2 d-none d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">ScheduleHub</h4>
                        <p class="text-white-50 small">调度管理系统</p>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" th:classappend="${currentPage == 'schedulers' ? 'active' : ''}"
                               href="/schedulers">
                                <i class="bi bi-clock-history"></i>
                                调度器管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:classappend="${currentPage == 'datasources' ? 'active' : ''}"
                               href="/schedulers/datasources">
                                <i class="bi bi-database"></i>
                                数据源管理
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-10 ms-sm-auto main-content">
                <!-- 顶部导航栏 -->
                <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
                    <div class="container-fluid">
                        <button class="navbar-toggler d-md-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu">
                            <span class="navbar-toggler-icon"></span>
                        </button>
                        <span class="navbar-brand mb-0 h1" th:text="${pageTitle ?: '调度管理系统'}">调度管理系统</span>
                        <div class="navbar-nav ms-auto">
                            <span class="navbar-text">
                                <i class="bi bi-person-circle"></i> 管理员
                            </span>
                        </div>
                    </div>
                </nav>

                <!-- 页面内容 -->
                <div class="container-fluid py-4">
                    <div th:insert="~{::content}"></div>
                </div>
            </main>
        </div>
    </div>

    <!-- 数据源选择组件模态框 -->
    <div class="modal fade" id="dataSourceSelectorModal" tabindex="-1" aria-labelledby="dataSourceSelectorModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="dataSourceSelectorModalLabel">选择数据源</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="dataSourceSelect" class="form-label">数据源</label>
                        <div class="input-group">
                            <select class="form-select" id="dataSourceSelect">
                                <option value="">请选择数据源（留空使用主数据源）</option>
                            </select>
                            <button class="btn btn-outline-primary" type="button" id="createDataSourceBtn">
                                <i class="bi bi-plus"></i> 创建新数据源
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmDataSourceBtn">确定</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据源创建/编辑模态框 -->
    <div class="modal fade" id="dataSourceFormModal" tabindex="-1" aria-labelledby="dataSourceFormModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="dataSourceFormModalLabel">创建数据源</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="dataSourceForm">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="dsId" class="form-label">数据源ID <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="dsId" name="id" required>
                                <div class="form-text">唯一标识符，只能包含字母、数字和下划线</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="dsType" class="form-label">数据库类型 <span class="text-danger">*</span></label>
                                <select class="form-select" id="dsType" name="dataSourceType" required>
                                    <option value="">请选择数据库类型</option>
                                    <option value="ORACLE">Oracle</option>
                                    <option value="MYSQL">MySQL</option>
                                    <option value="SQL_SERVER">SQL Server</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="dsHost" class="form-label">主机地址 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="dsHost" name="dataSourceHost" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="dsPort" class="form-label">端口 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="dsPort" name="dataSourcePort" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="dsInstance" class="form-label">数据库实例 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="dsInstance" name="dataSourceInstance" required>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="dsUser" class="form-label">用户名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="dsUser" name="dataSourceUser" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="dsPassword" class="form-label">密码 <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="dsPassword" name="dataSourcePassword" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="dsDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="dsDescription" name="description" rows="2"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-secondary" id="testConnectionBtn">
                            <i class="bi bi-wifi"></i> 测试连接
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="/js/bootstrap.bundle.min.js"></script>
    <script src="/js/datasource-selector.js"></script>
</body>
</html>
