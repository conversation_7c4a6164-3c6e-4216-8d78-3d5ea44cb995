<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调度器管理</title>
    <link href="/css/bootstrap.min.css" rel="stylesheet">
    <link href="/fonts/googleapis-css.css" rel="stylesheet">
    <link href="/fonts/bootstrap-icons.css" rel="stylesheet">
    <link href="/css/schedule-hub.css" rel="stylesheet">
</head>
<body class="fade-in">
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: var(--gradient-primary);">
        <div class="container">
            <a class="navbar-brand" href="/schedulers">
                ScheduleHub 调度管理系统
            </a>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题和操作按钮 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="page-title">
                    番禺中心医院-消息推送调度器管理
                </h1>
            </div>
            <div>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createSchedulerModal">
                     创建调度器
                </button>
            </div>
        </div>

        <!-- 状态概览卡片 -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">总调度器数</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" th:text="${schedulers.size()}">0</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">活动调度器</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" th:text="${schedulers.?[enabled].size()}">0</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">暂停调度器</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" th:text="${schedulers.?[!enabled].size()}">0</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">下次执行</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <th:block th:if="${!schedulers.empty && schedulers.?[enabled && nextExecutionTime != null].size() > 0}">
                                        <span th:text="${#temporals.format(schedulers.?[enabled && nextExecutionTime != null][0].nextExecutionTime, 'HH:mm:ss')}"></span>
                                    </th:block>
                                    <span th:if="${schedulers.empty || schedulers.?[enabled && nextExecutionTime != null].size() == 0}" class="text-muted">无</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">调度器列表</h5>
            </div>
            <div class="card-body">
                <div th:if="${schedulers.empty}" class="alert alert-info">
                    没有找到调度器。创建一个新的调度器开始使用。
                </div>

                <div th:unless="${schedulers.empty}" class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>状态</th>
                                <th>名称</th>
                                <th>Cron 表达式</th>
                                <th>上次执行</th>
                                <th>下次执行</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="scheduler : ${schedulers}">
                                <td>
                                    <span class="scheduler-status" th:classappend="${scheduler.enabled ? 'active' : 'inactive'}"
                                          th:title="${scheduler.enabled ? '活动' : '停用'}"></span>
                                    <span th:text="${scheduler.enabled ? '活动' : '停用'}"
                                          th:class="${scheduler.enabled ? 'text-success' : 'text-danger'}"></span>
                                </td>
                                <td>
                                    <a th:href="@{/schedulers/{id}(id=${scheduler.id})}" class="text-primary fw-bold" th:text="${scheduler.name}"></a>
                                    <p class="text-muted small mb-0" th:text="${scheduler.description}"></p>
                                </td>
                                <td>
                                    <code class="bg-light p-1 rounded" th:text="${scheduler.cronExpression}"></code>
                                </td>
                                <td>
                                    <div th:if="${scheduler.lastExecutionTime != null}">
                                        <span class="text-dark" th:text="${#temporals.format(scheduler.lastExecutionTime, 'yyyy-MM-dd')}"></span><br>
                                        <small class="text-muted" th:text="${#temporals.format(scheduler.lastExecutionTime, 'HH:mm:ss')}"></small>
                                    </div>
                                    <span th:unless="${scheduler.lastExecutionTime != null}" class="text-muted">从未执行</span>
                                </td>
                                <td>
                                    <div th:if="${scheduler.nextExecutionTime != null}">
                                        <span class="text-dark" th:text="${#temporals.format(scheduler.nextExecutionTime, 'yyyy-MM-dd')}"></span><br>
                                        <small class="text-muted" th:text="${#temporals.format(scheduler.nextExecutionTime, 'HH:mm:ss')}"></small>
                                    </div>
                                    <span th:unless="${scheduler.nextExecutionTime != null}" class="text-muted">无</span>
                                </td>
                                <td>
                                    <div class="d-flex">
                                        <a th:href="@{/schedulers/{id}(id=${scheduler.id})}" class="btn btn-sm btn-outline-primary me-1">
                                            详情
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-secondary me-1"
                                                data-bs-toggle="modal" th:data-bs-target="'#editSchedulerModal-' + ${scheduler.id}">
                                            编辑
                                        </button>
                                        <form th:if="${scheduler.enabled}" th:action="@{/schedulers/{id}/pause(id=${scheduler.id})}" method="post" class="me-1">
                                            <button type="submit" class="btn btn-sm btn-outline-warning">
                                                暂停
                                            </button>
                                        </form>
                                        <form th:unless="${scheduler.enabled}" th:action="@{/schedulers/{id}/start(id=${scheduler.id})}" method="post" class="me-1">
                                            <button type="submit" class="btn btn-sm btn-outline-success">
                                                启动
                                            </button>
                                        </form>
                                        <form th:action="@{/schedulers/{id}/run(id=${scheduler.id})}" method="post">
                                            <button type="submit" class="btn btn-sm btn-outline-primary">
                                                执行
                                            </button>
                                        </form>
                                        <form th:action="@{/schedulers/{id}/delete(id=${scheduler.id})}" method="post">
                                            <button type="submit" class="btn btn-sm btn-outline-danger">
                                                删除
                                            </button>
                                        </form>
                                    </div>

                                    <!-- Edit Scheduler Modal -->
                                    <div class="modal fade" th:id="'editSchedulerModal-' + ${scheduler.id}" tabindex="-1" aria-labelledby="'editSchedulerModalLabel-' + ${scheduler.id}" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" th:id="'editSchedulerModalLabel-' + ${scheduler.id}">编辑调度器</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <form th:action="@{/schedulers/{id}/update(id=${scheduler.id})}" method="post">
                                                    <div class="modal-body">
                                                        <div class="mb-3">
                                                            <label th:for="'name-' + ${scheduler.id}" class="form-label">名称 <span class="text-danger">*</span></label>
                                                            <input type="text" class="form-control" th:id="'name-' + ${scheduler.id}" name="name"
                                                                   th:value="${scheduler.name}" required>
                                                            <div class="form-text">调度器的名称，用于显示和识别</div>
                                                        </div>

                                                        <div class="mb-3">
                                                            <label th:for="'description-' + ${scheduler.id}" class="form-label">描述</label>
                                                            <textarea class="form-control" th:id="'description-' + ${scheduler.id}" name="description" rows="2"
                                                                      th:text="${scheduler.description}"></textarea>
                                                            <div class="form-text">可选：调度器的详细描述</div>
                                                        </div>

                                                        <div class="mb-3">
                                                            <label th:for="'cronExpression-' + ${scheduler.id}" class="form-label">Cron 表达式 <span class="text-danger">*</span></label>
                                                            <input type="text" class="form-control form-control-lg font-monospace" th:id="'cronExpression-' + ${scheduler.id}" name="cronExpression"
                                                                   th:value="${scheduler.cronExpression}" style="font-size: 1.1rem;" required>
                                                            <div class="form-text">
                                                                <div class="row">
                                                                    <div class="col-md-6">
                                                                        <strong>格式:</strong> <code>秒 分 时 日 月 周</code>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <strong>常用示例:</strong>
                                                                        <ul class="mb-0">
                                                                            <li><code>0 0 8 * * ?</code> - 每天上午8点</li>
                                                                            <li><code>0 0/30 * * * ?</code> - 每30分钟</li>
                                                                            <li><code>0 0/5 * * * ?</code> - 每5分钟</li>
                                                                        </ul>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="mb-3">
                                                            <label th:for="'wxApiKey-' + ${scheduler.id}" class="form-label">微信API密钥 <span class="text-danger">*</span></label>
                                                            <input type="text" class="form-control" th:id="'wxApiKey-' + ${scheduler.id}" name="wxApiKey"
                                                                   th:value="${scheduler.wxApiKey}" required>
                                                            <div class="form-text">用于发送微信消息的API密钥</div>
                                                        </div>

                                                        <div class="mb-4">
                                                            <label th:for="'sql-' + ${scheduler.id}" class="form-label">SQL查询 <span class="text-danger">*</span></label>
                                                            <textarea class="form-control font-monospace" th:id="'sql-' + ${scheduler.id}" name="sql" rows="15"
                                                                      style="font-size: 1rem; line-height: 1.5;" required th:text="${scheduler.sql}"></textarea>
                                                            <div class="form-text">调度器执行的SQL查询语句，可以包含复杂的查询逻辑</div>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                        <button type="submit" class="btn btn-primary">保存更改</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建调度器模态框 -->
    <div class="modal fade" id="createSchedulerModal" tabindex="-1" aria-labelledby="createSchedulerModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createSchedulerModalLabel">创建新调度器</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form th:action="@{/schedulers/create}" method="post">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <div class="form-text">调度器的名称，用于显示和识别</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="cronExpression" class="form-label">Cron 表达式 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="cronExpression" name="cronExpression"
                                       value="0 0 * * * ?" required>
                                <div class="form-text">
                                    格式: <code>秒 分 时 日 月 周</code>
                                    示例: <code>0 0 8 * * ?</code>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">描述</label>
                            <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                            <div class="form-text">可选：调度器的详细描述</div>
                        </div>

                        <div class="mb-3">
                            <label for="wxApiKey" class="form-label">微信API密钥 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="wxApiKey" name="wxApiKey" required>
                            <div class="form-text">用于发送微信消息的API密钥</div>
                        </div>

                        <div class="mb-4">
                            <label for="sql" class="form-label">SQL查询 <span class="text-danger">*</span></label>
                            <textarea class="form-control font-monospace" id="sql" name="sql" rows="15" style="font-size: 1rem; line-height: 1.5;" required></textarea>
                            <div class="form-text">调度器执行的SQL查询语句，可以包含复杂的查询逻辑</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">创建</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="/js/bootstrap.bundle.min.js"></script>
    <script src="/js/schedule-hub.js"></script>
</body>
</html>
