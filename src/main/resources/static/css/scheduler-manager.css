/* 调度器管理系统自定义样式 */

/* 全局样式 */
:root {
    --primary-color: #4e73df;
    --secondary-color: #6c757d;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --light-color: #f8f9fc;
    --dark-color: #5a5c69;
    --gradient-primary: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    --gradient-success: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
    --gradient-info: linear-gradient(135deg, #36b9cc 0%, #258391 100%);
    --gradient-warning: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);
    --gradient-danger: linear-gradient(135deg, #e74a3b 0%, #be2617 100%);

    /* 文字颜色 */
    --text-primary: #5a5c69;
    --text-secondary: #858796;
    --text-success: #1cc88a;
    --text-info: #36b9cc;
    --text-warning: #f6c23e;
    --text-danger: #e74a3b;
}

body {
    background-color: #f8f9fc;
    font-family: "Nunito", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* 页面标题 */
.page-title {
    color: #5a5c69;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    margin-bottom: 1.5rem;
    transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2);
}

/* 卡片左侧边框样式 */
.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}

.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}

.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}

.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}

.border-left-danger {
    border-left: 0.25rem solid var(--danger-color) !important;
}

.card-header {
    background: var(--gradient-primary);
    color: white;
    border-bottom: none;
    padding: 1rem 1.25rem;
    border-top-left-radius: 0.5rem !important;
    border-top-right-radius: 0.5rem !important;
    font-weight: 600;
}

.card-header h5 {
    margin-bottom: 0;
    font-weight: 700;
}

/* 按钮样式 */
.btn {
    border-radius: 0.35rem;
    padding: 0.375rem 0.75rem;
    font-weight: 600;
    transition: all 0.2s;
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #3a5ccc 0%, #1a3a9c 100%);
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.btn-success {
    background: var(--gradient-success);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #19b378 0%, #0f6b4a 100%);
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.btn-warning {
    background: var(--gradient-warning);
    border: none;
    color: #fff;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e5b43a 0%, #c99209 100%);
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.btn-danger {
    background: var(--gradient-danger);
    border: none;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #d44235 0%, #a92215 100%);
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* 表格样式 */
.table {
    color: #5a5c69;
}

.table thead th {
    background-color: #f8f9fc;
    border-top: none;
    border-bottom: 2px solid #e3e6f0;
    font-weight: 700;
    color: #4e73df;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.05em;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(78, 115, 223, 0.05);
}

.table-hover tbody tr:hover {
    background-color: rgba(78, 115, 223, 0.1);
    transform: translateY(-1px);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.2s;
}

/* 状态指示器 */
.scheduler-status {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
    position: relative;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.scheduler-status.active {
    background-color: var(--success-color);
}

.scheduler-status.active::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: var(--success-color);
    opacity: 0.5;
    animation: pulse 2s infinite;
}

.scheduler-status.inactive {
    background-color: var(--danger-color);
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.5;
    }
    70% {
        transform: scale(1.5);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

/* 徽章样式 */
.badge {
    padding: 0.4em 0.6em;
    font-weight: 600;
    border-radius: 0.35rem;
}

.badge.bg-success {
    background: var(--gradient-success) !important;
}

.badge.bg-danger {
    background: var(--gradient-danger) !important;
}

.badge.bg-info {
    background: var(--gradient-info) !important;
}

.badge.bg-warning {
    background: var(--gradient-warning) !important;
    color: white;
}

/* 分页样式 */
.pagination .page-link {
    color: var(--primary-color);
    border-radius: 0.35rem;
    margin: 0 0.2rem;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.2s;
}

.pagination .page-link:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

.pagination .page-item.active .page-link {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
}

.pagination .page-item.disabled .page-link {
    color: #d1d3e2;
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.4);
    position: relative;
}

.modal-header {
    background: var(--gradient-primary);
    color: white;
    border-bottom: none;
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
}

.modal-footer {
    border-top: none;
}

/* 修复模态框跳动问题 */
.modal {
    padding-right: 0 !important;
}

.modal-dialog {
    height: auto;
    display: flex;
    align-items: center;
    margin-top: 0;
    margin-bottom: 0;
    max-width: 1000px !important;
    width: 1000px !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

.modal-open {
    overflow: auto !important;
    padding-right: 0 !important;
}

/* 表单样式 */
.form-control {
    border-radius: 0.35rem;
    border: 1px solid #d1d3e2;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
}

/* 时间过滤按钮组 */
.time-filter-group .btn {
    border-radius: 0.35rem;
    margin-right: 0.25rem;
    font-size: 0.85rem;
    padding: 0.25rem 0.5rem;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 执行历史容器样式 */
.execution-history-container {
    display: flex;
    flex-direction: column;
    height: 600px; /* 总高度 */
    position: relative;
}

/* 表格容器样式 */
.execution-table-container {
    flex: 1;
    overflow-y: auto;
    min-height: 0; /* 必要的，使 flex 子元素可以正确收缩 */
    position: relative;
    margin-bottom: 0;
}

/* 表头固定样式 */
.execution-table-container thead.sticky-top {
    position: sticky;
    top: 0;
    z-index: 1;
}

.execution-table-container thead.sticky-top th {
    position: sticky;
    top: 0;
    background-color: #f8f9fc;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 分页容器样式 */
.pagination-container {
    position: sticky;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    flex-shrink: 0;
    background-color: #fff;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

/* 消息列样式 */
.message-cell {
    max-width: 400px;
    white-space: pre-line;
    word-break: break-word;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }

    .table-responsive {
        border: none;
    }
}
