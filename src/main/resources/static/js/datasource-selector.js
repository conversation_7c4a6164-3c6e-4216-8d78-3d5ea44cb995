/**
 * 数据源选择器组件
 */
class DataSourceSelector {
    constructor() {
        this.currentCallback = null;
        this.currentInputId = null;
        this.init();
    }

    init() {
        // 绑定事件
        document.getElementById('createDataSourceBtn')?.addEventListener('click', () => {
            this.showDataSourceForm();
        });

        document.getElementById('confirmDataSourceBtn')?.addEventListener('click', () => {
            this.confirmSelection();
        });

        document.getElementById('testConnectionBtn')?.addEventListener('click', () => {
            this.testConnection();
        });

        document.getElementById('dataSourceForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveDataSource();
        });

        // 加载数据源选项
        this.loadDataSourceOptions();
    }

    /**
     * 显示数据源选择器
     * @param {string} inputId - 目标输入框ID
     * @param {function} callback - 选择完成后的回调函数
     */
    show(inputId, callback) {
        this.currentInputId = inputId;
        this.currentCallback = callback;
        this.loadDataSourceOptions();
        
        const modal = new bootstrap.Modal(document.getElementById('dataSourceSelectorModal'));
        modal.show();
    }

    /**
     * 加载数据源选项
     */
    async loadDataSourceOptions() {
        try {
            const response = await fetch('/schedulers/datasources/options');
            const options = await response.json();
            
            const select = document.getElementById('dataSourceSelect');
            select.innerHTML = '<option value="">请选择数据源（留空使用主数据源）</option>';
            
            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.id;
                optionElement.textContent = option.name;
                select.appendChild(optionElement);
            });
        } catch (error) {
            console.error('加载数据源选项失败:', error);
            this.showAlert('加载数据源选项失败', 'danger');
        }
    }

    /**
     * 确认选择
     */
    confirmSelection() {
        const selectedValue = document.getElementById('dataSourceSelect').value;
        const selectedText = document.getElementById('dataSourceSelect').selectedOptions[0]?.textContent || '';
        
        if (this.currentCallback) {
            this.currentCallback(selectedValue, selectedText);
        }
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('dataSourceSelectorModal'));
        modal.hide();
    }

    /**
     * 显示数据源表单
     */
    showDataSourceForm() {
        // 清空表单
        document.getElementById('dataSourceForm').reset();
        document.getElementById('dataSourceFormModalLabel').textContent = '创建数据源';
        
        const modal = new bootstrap.Modal(document.getElementById('dataSourceFormModal'));
        modal.show();
    }

    /**
     * 保存数据源
     */
    async saveDataSource() {
        const form = document.getElementById('dataSourceForm');
        const formData = new FormData(form);
        
        const data = {
            id: formData.get('id'),
            dataSourceType: formData.get('dataSourceType'),
            dataSourceHost: formData.get('dataSourceHost'),
            dataSourcePort: formData.get('dataSourcePort'),
            dataSourceInstance: formData.get('dataSourceInstance'),
            dataSourceUser: formData.get('dataSourceUser'),
            dataSourcePassword: formData.get('dataSourcePassword'),
            description: formData.get('description')
        };

        try {
            const response = await fetch('/schedulers/datasources', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            if (response.ok) {
                this.showAlert('数据源保存成功', 'success');
                
                // 关闭表单模态框
                const formModal = bootstrap.Modal.getInstance(document.getElementById('dataSourceFormModal'));
                formModal.hide();
                
                // 重新加载数据源选项
                await this.loadDataSourceOptions();
                
                // 自动选择新创建的数据源
                document.getElementById('dataSourceSelect').value = data.id;
                
                this.showAlert('数据源创建成功，已自动选择', 'success');
            } else {
                const errorData = await response.json();
                this.showAlert(errorData.message || '保存失败', 'danger');
            }
        } catch (error) {
            console.error('保存数据源失败:', error);
            this.showAlert('保存数据源失败', 'danger');
        }
    }

    /**
     * 测试连接
     */
    async testConnection() {
        const form = document.getElementById('dataSourceForm');
        const formData = new FormData(form);
        
        const data = {
            id: formData.get('id'),
            dataSourceType: formData.get('dataSourceType'),
            dataSourceHost: formData.get('dataSourceHost'),
            dataSourcePort: formData.get('dataSourcePort'),
            dataSourceInstance: formData.get('dataSourceInstance'),
            dataSourceUser: formData.get('dataSourceUser'),
            dataSourcePassword: formData.get('dataSourcePassword'),
            description: formData.get('description')
        };

        const btn = document.getElementById('testConnectionBtn');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-hourglass-split"></i> 测试中...';
        btn.disabled = true;

        try {
            const response = await fetch('/schedulers/datasources/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();
            
            if (result.success) {
                this.showAlert('连接测试成功', 'success');
            } else {
                this.showAlert(result.message || '连接测试失败', 'danger');
            }
        } catch (error) {
            console.error('测试连接失败:', error);
            this.showAlert('测试连接失败', 'danger');
        } finally {
            btn.innerHTML = originalText;
            btn.disabled = false;
        }
    }

    /**
     * 显示提示信息
     */
    showAlert(message, type = 'info') {
        // 移除现有的提示
        const existingAlert = document.querySelector('.alert-floating');
        if (existingAlert) {
            existingAlert.remove();
        }

        // 创建新的提示
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show alert-floating`;
        alert.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
        `;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alert);

        // 3秒后自动消失
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 3000);
    }
}

// 全局实例
window.dataSourceSelector = new DataSourceSelector();

/**
 * 创建数据源选择输入框
 * @param {string} containerId - 容器ID
 * @param {string} inputName - 输入框name属性
 * @param {string} currentValue - 当前值
 * @param {string} label - 标签文本
 */
function createDataSourceInput(containerId, inputName, currentValue = '', label = '数据源') {
    const container = document.getElementById(containerId);
    if (!container) return;

    container.innerHTML = `
        <label for="${inputName}" class="form-label">${label}</label>
        <div class="input-group">
            <input type="text" class="form-control" id="${inputName}" name="${inputName}" 
                   value="${currentValue}" readonly placeholder="点击选择数据源（留空使用主数据源）">
            <button class="btn btn-outline-primary" type="button" onclick="selectDataSource('${inputName}')">
                <i class="bi bi-search"></i> 选择
            </button>
            <button class="btn btn-outline-secondary" type="button" onclick="clearDataSource('${inputName}')">
                <i class="bi bi-x"></i>
            </button>
        </div>
        <div class="form-text">选择调度器使用的数据源，留空则使用主数据源</div>
    `;
}

/**
 * 选择数据源
 */
function selectDataSource(inputId) {
    window.dataSourceSelector.show(inputId, (value, text) => {
        const input = document.getElementById(inputId);
        if (input) {
            input.value = value;
            input.setAttribute('data-text', text);
        }
    });
}

/**
 * 清除数据源选择
 */
function clearDataSource(inputId) {
    const input = document.getElementById(inputId);
    if (input) {
        input.value = '';
        input.removeAttribute('data-text');
    }
}
