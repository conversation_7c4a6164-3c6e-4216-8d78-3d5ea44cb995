/**
 * ScheduleHub 前端交互脚本
 */

// 添加加载指示器和遮罩层的CSS
document.head.insertAdjacentHTML('beforeend', `
<style>
.spinner-border {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  vertical-align: text-bottom;
  border: 0.2em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner-border .75s linear infinite;
}

@keyframes spinner-border {
  to { transform: rotate(360deg); }
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.2em;
}

/* 遮罩层样式 */
.page-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.page-overlay.active {
  opacity: 1;
  visibility: visible;
}

.page-overlay .spinner-container {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  text-align: center;
}

.page-overlay .spinner-border {
  width: 3rem;
  height: 3rem;
  margin-bottom: 1rem;
  color: var(--bs-primary, #0d6efd);
}

.page-overlay .loading-text {
  font-size: 1.2rem;
  color: #333;
  margin: 0;
}
</style>
`);

// 防抖函数：限制函数在一定时间内只能执行一次
function debounce(func, wait = 300) {
    let timeout;
    return function(...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            func.apply(context, args);
        }, wait);
    };
}

// 节流函数：限制函数在一定时间内只能执行一次，但会立即执行第一次
function throttle(func, wait = 300) {
    let timeout = null;
    let lastRun = 0;
    return function(...args) {
        const context = this;
        if (!timeout) {
            func.apply(context, args);
            lastRun = Date.now();
            timeout = setTimeout(() => {
                timeout = null;
            }, wait);
        }
    };
}

// 页面遮罩层管理器
const overlayManager = {
    overlay: null,
    loadingText: null,
    counter: 0,

    // 初始化遮罩层
    init: function() {
        // 创建遮罩层元素
        this.overlay = document.createElement('div');
        this.overlay.className = 'page-overlay';

        // 创建加载容器
        const spinnerContainer = document.createElement('div');
        spinnerContainer.className = 'spinner-container';

        // 创建加载图标
        const spinner = document.createElement('div');
        spinner.className = 'spinner-border';
        spinner.setAttribute('role', 'status');

        // 创建加载文本
        this.loadingText = document.createElement('p');
        this.loadingText.className = 'loading-text';
        this.loadingText.textContent = '正在处理请求...';

        // 组装元素
        spinnerContainer.appendChild(spinner);
        spinnerContainer.appendChild(this.loadingText);
        this.overlay.appendChild(spinnerContainer);

        // 添加到文档中
        document.body.appendChild(this.overlay);
    },

    // 显示遮罩层
    show: function(message = '正在处理请求...') {
        this.counter++;

        if (!this.overlay) {
            this.init();
        }

        this.loadingText.textContent = message;
        this.overlay.classList.add('active');
        document.body.style.overflow = 'hidden'; // 禁止滚动
    },

    // 隐藏遮罩层
    hide: function() {
        this.counter--;

        if (this.counter <= 0) {
            this.counter = 0;
            if (this.overlay) {
                this.overlay.classList.remove('active');
                document.body.style.overflow = ''; // 恢复滚动
            }
        }
    },

    // 强制隐藏遮罩层（无论计数器状态）
    forceHide: function() {
        this.counter = 0;
        if (this.overlay) {
            this.overlay.classList.remove('active');
            document.body.style.overflow = ''; // 恢复滚动
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // 初始化遮罩层
    overlayManager.init();

    // 添加安全机制，确保页面加载完成后遮罩层不会卡住
    window.addEventListener('load', function() {
        // 如果页面加载完成后还有遮罩层，10秒后强制隐藏
        setTimeout(function() {
            overlayManager.forceHide();
        }, 10000);
    });

    // 完全重写模态框逻辑，使用iframe方案解决闪动问题

    // 创建一个简单的模态框管理器
    const modalManager = {
        // 当前打开的模态框
        currentModal: null,
        // 当前的遮罩层
        currentBackdrop: null,
        // 当前的iframe容器
        currentContainer: null,

        // 打开模态框
        openModal: function(modalId) {
            // 先关闭当前打开的模态框（如果有）
            this.closeCurrentModal();

            // 获取模态框元素
            const modal = document.querySelector(modalId);
            if (!modal) return;

            // 创建一个固定容器，完全隔离页面上的其他元素
            const container = document.createElement('div');
            container.style.position = 'fixed';
            container.style.top = '0';
            container.style.left = '0';
            container.style.width = '100%';
            container.style.height = '100%';
            container.style.zIndex = '2000';
            container.style.pointerEvents = 'none';
            document.body.appendChild(container);

            // 创建遮罩层
            const backdrop = document.createElement('div');
            backdrop.className = 'modal-backdrop';
            backdrop.style.position = 'fixed';
            backdrop.style.top = '0';
            backdrop.style.left = '0';
            backdrop.style.width = '100%';
            backdrop.style.height = '100%';
            backdrop.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            backdrop.style.zIndex = '1';
            backdrop.style.pointerEvents = 'auto';
            container.appendChild(backdrop);

            // 创建模态框容器
            const modalContainer = document.createElement('div');
            modalContainer.className = 'modal-container';
            modalContainer.style.position = 'fixed';
            modalContainer.style.top = '50%';
            modalContainer.style.left = '50%';
            modalContainer.style.transform = 'translate(-50%, -50%)';
            modalContainer.style.zIndex = '2';
            modalContainer.style.width = '1000px';
            modalContainer.style.maxWidth = '95%';
            modalContainer.style.pointerEvents = 'auto';
            container.appendChild(modalContainer);

            // 克隆模态框内容到新容器
            const modalContent = modal.querySelector('.modal-content').cloneNode(true);
            modalContainer.appendChild(modalContent);

            // 设置当前模态框和遮罩层
            this.currentModal = modal;
            this.currentBackdrop = backdrop;
            this.currentContainer = container;

            // 禁用页面滚动并添加模态框打开类
            document.body.style.overflow = 'hidden';
            document.body.classList.add('modal-open');

            // 点击遮罩层关闭
            backdrop.addEventListener('click', () => this.closeCurrentModal());

            // 设置关闭按钮事件
            const closeButtons = modalContent.querySelectorAll('[data-bs-dismiss="modal"]');
            closeButtons.forEach(btn => {
                btn.addEventListener('click', () => this.closeCurrentModal());
            });

            // 设置表单提交事件（使用防抖机制）
            const forms = modalContent.querySelectorAll('form');
            forms.forEach(form => {
                const originalForm = modal.querySelector(`form[action="${form.getAttribute('action')}"]`);
                form.addEventListener('submit', function(event) {
                    event.preventDefault();

                    // 防止重复提交
                    if (form.dataset.isSubmitting === 'true') {
                        return false;
                    }

                    // 标记表单正在提交
                    form.dataset.isSubmitting = 'true';

                    // 显示遮罩层
                    overlayManager.show('正在提交表单...');

                    // 禁用所有提交按钮
                    const submitButtons = form.querySelectorAll('button[type="submit"]');
                    submitButtons.forEach(btn => {
                        btn.disabled = true;
                        // 添加加载指示器
                        const originalText = btn.innerHTML;
                        btn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 处理中...';

                        // 延迟执行以显示加载状态
                        setTimeout(() => {
                            try {
                                // 将表单数据复制到原始表单
                                const formData = new FormData(form);
                                for (let [key, value] of formData.entries()) {
                                    const input = originalForm.querySelector(`[name="${key}"]`);
                                    if (input) input.value = value;
                                }
                                // 提交原始表单
                                originalForm.submit();
                                // 关闭模态框
                                modalManager.closeCurrentModal();
                            } catch (error) {
                                console.error('表单提交错误:', error);
                                // 隐藏遮罩层
                                overlayManager.hide();
                                // 重置表单状态
                                form.dataset.isSubmitting = 'false';
                                submitButtons.forEach(btn => {
                                    btn.disabled = false;
                                    btn.innerHTML = originalText;
                                });
                                alert('表单提交失败，请重试');
                            }
                        }, 300);
                    });
                });
            });
        },

        // 关闭当前模态框
        closeCurrentModal: function() {
            if (this.currentContainer) {
                document.body.removeChild(this.currentContainer);
                this.currentContainer = null;
                this.currentModal = null;
                this.currentBackdrop = null;
            }

            document.body.style.overflow = '';
            document.body.classList.remove('modal-open');

            // 确保在关闭模态框时也隐藏遮罩层
            // 注意：我们使用延迟，因为在表单提交时我们需要保持遮罩层可见
            setTimeout(() => {
                overlayManager.hide();
            }, 500);
        }
    };

    // 获取所有编辑按钮
    const editButtons = document.querySelectorAll('button[data-bs-toggle="modal"]');

    // 为每个编辑按钮添加点击事件
    editButtons.forEach(button => {
        // 获取模态框ID
        const modalId = button.getAttribute('data-bs-target');

        // 移除Bootstrap的模态框属性
        button.removeAttribute('data-bs-toggle');
        button.removeAttribute('data-bs-target');

        // 添加自定义点击事件（使用防抖机制）
        button.addEventListener('click', debounce(function(e) {
            e.preventDefault();
            e.stopPropagation();

            // 使用模态框管理器打开模态框
            modalManager.openModal(modalId);
        }, 500));
    });

    // 添加ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            modalManager.closeCurrentModal();
        }
    });

    // 禁用页面上所有的鼠标移动事件，防止影响模态框
    document.addEventListener('mousemove', function(e) {
        if (modalManager.currentContainer) {
            e.stopPropagation();
        }
    }, true);

    // 为所有表单添加防抖提交机制
    const allForms = document.querySelectorAll('form');
    allForms.forEach(form => {
        // 跳过模态框中的表单，因为它们已经在模态框管理器中处理
        if (!form.closest('.modal-content')) {
            // 保存原始的提交事件
            const originalSubmit = form.onsubmit;

            // 覆盖提交事件
            form.onsubmit = function(e) {
                // 防止重复提交
                if (form.dataset.isSubmitting === 'true') {
                    e.preventDefault();
                    return false;
                }

                // 标记表单正在提交
                form.dataset.isSubmitting = 'true';

                // 显示遮罩层
                const formAction = form.getAttribute('action') || '';
                let actionText = '正在提交...';

                // 根据表单操作显示不同的提示文本
                if (formAction.includes('/pause')) {
                    actionText = '正在暂停调度器...';
                } else if (formAction.includes('/start')) {
                    actionText = '正在启动调度器...';
                } else if (formAction.includes('/run')) {
                    actionText = '正在执行调度器...';
                }

                overlayManager.show(actionText);

                // 禁用所有提交按钮
                const submitButtons = form.querySelectorAll('button[type="submit"]');
                submitButtons.forEach(btn => {
                    btn.disabled = true;
                    // 添加加载指示器
                    btn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 处理中...';
                });

                // 允许表单提交
                setTimeout(() => {
                    // 重置表单状态（以防表单提交失败）
                    form.dataset.isSubmitting = 'false';
                    submitButtons.forEach(btn => {
                        btn.disabled = false;
                        // 恢复原始按钮文本
                        btn.innerHTML = btn.dataset.originalText || '提交';
                    });
                    // 隐藏遮罩层（如果请求失败）
                    setTimeout(() => {
                        overlayManager.hide();
                    }, 5000); // 5秒后隐藏遮罩层，防止请求失败时卡死
                }, 2000); // 2秒后重置按钮状态

                // 调用原始提交事件（如果有）
                if (typeof originalSubmit === 'function') {
                    return originalSubmit.call(this, e);
                }
            };

            // 保存所有提交按钮的原始文本
            const buttons = form.querySelectorAll('button[type="submit"]');
            buttons.forEach(btn => {
                btn.dataset.originalText = btn.innerHTML;
            });
        }
    });

    // 为所有按钮添加防抖点击事件
    const allButtons = document.querySelectorAll('button:not([data-no-debounce])');
    allButtons.forEach(button => {
        // 跳过已经处理过的按钮和表单提交按钮（因为它们已经在表单提交中处理）
        if (!button.dataset.debounceApplied &&
            !button.closest('.modal-content') &&
            button.type !== 'submit' &&
            !button.closest('form')) {

            const originalClick = button.onclick;

            // 使用节流而非防抖，因为我们希望立即响应第一次点击
            button.onclick = throttle(function(e) {
                // 显示遮罩层（如果按钮不是用于取消操作）
                if (!button.classList.contains('btn-secondary') &&
                    !button.classList.contains('btn-close') &&
                    !button.hasAttribute('data-bs-dismiss')) {

                    // 根据按钮文本或类型决定显示的消息
                    let actionText = '正在处理...';
                    const buttonText = button.textContent.trim().toLowerCase();

                    if (buttonText.includes('详情') || buttonText.includes('detail')) {
                        actionText = '正在加载详情...';
                    } else if (buttonText.includes('编辑') || buttonText.includes('edit')) {
                        actionText = '正在加载编辑器...';
                    } else if (buttonText.includes('执行') || buttonText.includes('run')) {
                        actionText = '正在执行操作...';
                    }

                    overlayManager.show(actionText);

                    // 添加安全机制，防止请求失败时遮罩层卡死
                    setTimeout(() => {
                        overlayManager.hide();
                    }, 8000); // 8秒后自动隐藏遮罩层
                }

                if (typeof originalClick === 'function') {
                    return originalClick.call(this, e);
                }
            }, 500);

            button.dataset.debounceApplied = 'true';
        }
    });
});
