/**
 * 调度器 AJAX 操作脚本
 */
document.addEventListener('DOMContentLoaded', function() {
    // 初始化遮罩层
    const overlay = document.createElement('div');
    overlay.className = 'ajax-overlay';
    overlay.innerHTML = `
        <div class="spinner-container">
            <div class="spinner-border text-primary" role="status"></div>
            <p class="mt-2 loading-text">处理中...</p>
        </div>
    `;
    document.body.appendChild(overlay);
    
    // 显示遮罩层
    function showOverlay(message = '处理中...') {
        const loadingText = overlay.querySelector('.loading-text');
        loadingText.textContent = message;
        overlay.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
    
    // 隐藏遮罩层
    function hideOverlay() {
        overlay.classList.remove('active');
        document.body.style.overflow = '';
    }
    
    // 显示通知
    function showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} notification`;
        notification.innerHTML = message;
        document.body.appendChild(notification);
        
        // 显示通知
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // 自动关闭通知
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
    
    // 处理表单 AJAX 提交
    function handleFormSubmit(form, successCallback) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 获取表单数据
            const formData = new FormData(form);
            const url = form.getAttribute('action');
            const method = form.getAttribute('method') || 'POST';
            
            // 获取按钮文本，用于显示加载消息
            const submitButton = form.querySelector('button[type="submit"]');
            const buttonText = submitButton.textContent.trim();
            
            // 显示遮罩层
            showOverlay(`正在${buttonText}...`);
            
            // 发送 AJAX 请求
            fetch(url, {
                method: method,
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`请求失败: ${response.status}`);
                }
                return response.text();
            })
            .then(data => {
                // 隐藏遮罩层
                hideOverlay();
                
                // 显示成功通知
                showNotification(`${buttonText}成功！`, 'success');
                
                // 执行成功回调
                if (typeof successCallback === 'function') {
                    successCallback(data);
                }
            })
            .catch(error => {
                // 隐藏遮罩层
                hideOverlay();
                
                // 显示错误通知
                showNotification(`操作失败: ${error.message}`, 'danger');
                console.error('Error:', error);
            });
        });
    }
    
    // 处理所有操作按钮
    function setupActionButtons() {
        // 暂停按钮
        const pauseForm = document.querySelector('form[action*="/pause"]');
        if (pauseForm) {
            handleFormSubmit(pauseForm, function() {
                // 刷新页面状态
                refreshSchedulerStatus();
            });
        }
        
        // 启动按钮
        const startForm = document.querySelector('form[action*="/start"]');
        if (startForm) {
            handleFormSubmit(startForm, function() {
                // 刷新页面状态
                refreshSchedulerStatus();
            });
        }
        
        // 立即执行按钮
        const runForm = document.querySelector('form[action*="/run"]');
        if (runForm) {
            handleFormSubmit(runForm, function() {
                // 刷新执行历史
                refreshExecutionHistory();
            });
        }
    }
    
    // 刷新调度器状态
    function refreshSchedulerStatus() {
        const schedulerId = getSchedulerId();
        if (!schedulerId) return;
        
        fetch(`/api/schedulers/${schedulerId}/status`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            // 更新状态显示
            updateStatusDisplay(data);
            
            // 更新按钮显示
            updateActionButtons(data);
        })
        .catch(error => {
            console.error('Error fetching scheduler status:', error);
            // 如果API不可用，刷新整个页面
            location.reload();
        });
    }
    
    // 刷新执行历史
    function refreshExecutionHistory() {
        const schedulerId = getSchedulerId();
        if (!schedulerId) return;
        
        // 获取当前分页参数
        const urlParams = new URLSearchParams(window.location.search);
        const timeRange = urlParams.get('timeRange') || 'today';
        const page = urlParams.get('page') || '0';
        const size = urlParams.get('size') || '10';
        
        fetch(`/api/schedulers/${schedulerId}/executions?timeRange=${timeRange}&page=${page}&size=${size}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            // 更新执行历史表格
            updateExecutionHistoryTable(data);
        })
        .catch(error => {
            console.error('Error fetching execution history:', error);
            // 如果API不可用，刷新整个页面
            location.reload();
        });
    }
    
    // 获取调度器ID
    function getSchedulerId() {
        const path = window.location.pathname;
        const matches = path.match(/\/schedulers\/([^\/]+)/);
        return matches ? matches[1] : null;
    }
    
    // 更新状态显示
    function updateStatusDisplay(data) {
        // 这里需要根据实际DOM结构进行更新
        // 由于没有API，这里只是示例
        console.log('Would update status display with:', data);
    }
    
    // 更新按钮显示
    function updateActionButtons(data) {
        // 这里需要根据实际DOM结构进行更新
        // 由于没有API，这里只是示例
        console.log('Would update action buttons with:', data);
    }
    
    // 更新执行历史表格
    function updateExecutionHistoryTable(data) {
        // 这里需要根据实际DOM结构进行更新
        // 由于没有API，这里只是示例
        console.log('Would update execution history table with:', data);
    }
    
    // 设置操作按钮
    setupActionButtons();
    
    // 添加CSS样式
    const style = document.createElement('style');
    style.textContent = `
        .ajax-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s, visibility 0.3s;
        }
        
        .ajax-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        
        .ajax-overlay .spinner-container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            text-align: center;
        }
        
        .ajax-overlay .spinner-border {
            width: 3rem;
            height: 3rem;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            padding: 15px 25px;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            opacity: 0;
            transform: translateY(-20px);
            transition: opacity 0.3s, transform 0.3s;
        }
        
        .notification.show {
            opacity: 1;
            transform: translateY(0);
        }
    `;
    document.head.appendChild(style);
});
