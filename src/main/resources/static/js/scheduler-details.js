/**
 * 调度器详情页面交互脚本
 */

// 添加加载指示器和遮罩层的CSS
document.head.insertAdjacentHTML('beforeend', `
<style>
.spinner-border {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  vertical-align: text-bottom;
  border: 0.2em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner-border .75s linear infinite;
}

@keyframes spinner-border {
  to { transform: rotate(360deg); }
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.2em;
}

/* 遮罩层样式 */
.page-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.page-overlay.active {
  opacity: 1;
  visibility: visible;
}

.page-overlay .spinner-container {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  text-align: center;
}

.page-overlay .spinner-border {
  width: 3rem;
  height: 3rem;
  margin-bottom: 1rem;
  color: var(--bs-primary, #0d6efd);
}

.page-overlay .loading-text {
  font-size: 1.2rem;
  color: #333;
  margin: 0;
}
</style>
`);

// 防抖函数：限制函数在一定时间内只能执行一次
function debounce(func, wait = 300) {
    let timeout;
    return function(...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            func.apply(context, args);
        }, wait);
    };
}

// 节流函数：限制函数在一定时间内只能执行一次，但会立即执行第一次
function throttle(func, wait = 300) {
    let timeout = null;
    let lastRun = 0;
    return function(...args) {
        const context = this;
        if (!timeout) {
            func.apply(context, args);
            lastRun = Date.now();
            timeout = setTimeout(() => {
                timeout = null;
            }, wait);
        }
    };
}

// 页面遮罩层管理器
const overlayManager = {
    overlay: null,
    loadingText: null,
    counter: 0,
    
    // 初始化遮罩层
    init: function() {
        // 创建遮罩层元素
        this.overlay = document.createElement('div');
        this.overlay.className = 'page-overlay';
        
        // 创建加载容器
        const spinnerContainer = document.createElement('div');
        spinnerContainer.className = 'spinner-container';
        
        // 创建加载图标
        const spinner = document.createElement('div');
        spinner.className = 'spinner-border';
        spinner.setAttribute('role', 'status');
        
        // 创建加载文本
        this.loadingText = document.createElement('p');
        this.loadingText.className = 'loading-text';
        this.loadingText.textContent = '正在处理请求...';
        
        // 组装元素
        spinnerContainer.appendChild(spinner);
        spinnerContainer.appendChild(this.loadingText);
        this.overlay.appendChild(spinnerContainer);
        
        // 添加到文档中
        document.body.appendChild(this.overlay);
    },
    
    // 显示遮罩层
    show: function(message = '正在处理请求...') {
        this.counter++;
        
        if (!this.overlay) {
            this.init();
        }
        
        this.loadingText.textContent = message;
        this.overlay.classList.add('active');
        document.body.style.overflow = 'hidden'; // 禁止滚动
    },
    
    // 隐藏遮罩层
    hide: function() {
        this.counter--;
        
        if (this.counter <= 0) {
            this.counter = 0;
            if (this.overlay) {
                this.overlay.classList.remove('active');
                document.body.style.overflow = ''; // 恢复滚动
            }
        }
    },
    
    // 强制隐藏遮罩层（无论计数器状态）
    forceHide: function() {
        this.counter = 0;
        if (this.overlay) {
            this.overlay.classList.remove('active');
            document.body.style.overflow = ''; // 恢复滚动
        }
    }
};

document.addEventListener('DOMContentLoaded', function() {
    // 初始化遮罩层
    overlayManager.init();
    
    // 添加安全机制，确保页面加载完成后遮罩层不会卡住
    window.addEventListener('load', function() {
        // 如果页面加载完成后还有遮罩层，10秒后强制隐藏
        setTimeout(function() {
            overlayManager.forceHide();
        }, 10000);
    });
    
    // 为所有表单添加防抖提交机制
    const allForms = document.querySelectorAll('form');
    allForms.forEach(form => {
        // 保存原始的提交事件
        const originalSubmit = form.onsubmit;
        
        // 覆盖提交事件
        form.onsubmit = function(e) {
            // 防止重复提交
            if (form.dataset.isSubmitting === 'true') {
                e.preventDefault();
                return false;
            }
            
            // 标记表单正在提交
            form.dataset.isSubmitting = 'true';
            
            // 显示遮罩层
            const formAction = form.getAttribute('action') || '';
            let actionText = '正在提交...';
            
            // 根据表单操作显示不同的提示文本
            if (formAction.includes('/pause')) {
                actionText = '正在暂停调度器...';
            } else if (formAction.includes('/start')) {
                actionText = '正在启动调度器...';
            } else if (formAction.includes('/run')) {
                actionText = '正在执行调度器...';
            } else if (formAction.includes('/update')) {
                actionText = '正在更新调度器...';
            }
            
            overlayManager.show(actionText);
            
            // 禁用所有提交按钮
            const submitButtons = form.querySelectorAll('button[type="submit"]');
            submitButtons.forEach(btn => {
                btn.disabled = true;
                // 保存原始文本
                const originalText = btn.innerHTML;
                btn.dataset.originalText = originalText;
                // 添加加载指示器
                btn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 处理中...';
            });
            
            // 允许表单提交，但添加安全机制
            setTimeout(() => {
                // 重置表单状态（以防表单提交失败）
                form.dataset.isSubmitting = 'false';
                submitButtons.forEach(btn => {
                    btn.disabled = false;
                    // 恢复原始按钮文本
                    btn.innerHTML = btn.dataset.originalText || '提交';
                });
                // 隐藏遮罩层（如果请求失败）
                setTimeout(() => {
                    overlayManager.hide();
                }, 5000); // 5秒后隐藏遮罩层，防止请求失败时卡死
            }, 2000); // 2秒后重置按钮状态
            
            // 调用原始提交事件（如果有）
            if (typeof originalSubmit === 'function') {
                return originalSubmit.call(this, e);
            }
        };
    });
    
    // 为所有链接按钮添加防抖和遮罩
    const allLinks = document.querySelectorAll('a.btn, a.page-link');
    allLinks.forEach(link => {
        // 跳过已经处理过的链接和返回首页的链接
        if (!link.dataset.debounceApplied && 
            !link.classList.contains('disabled') && 
            !link.href.includes('/schedulers"') && 
            !link.href.endsWith('/schedulers')) {
            
            // 使用节流而非防抖，因为我们希望立即响应第一次点击
            link.addEventListener('click', throttle(function(e) {
                // 显示遮罩层（如果链接不是用于取消操作）
                if (!link.classList.contains('btn-secondary')) {
                    // 根据链接文本或类型决定显示的消息
                    let actionText = '正在加载...';
                    const linkText = link.textContent.trim().toLowerCase();
                    const linkHref = link.getAttribute('href') || '';
                    
                    if (linkHref.includes('timeRange=')) {
                        actionText = '正在筛选数据...';
                    } else if (linkHref.includes('page=')) {
                        actionText = '正在加载数据...';
                    } else if (linkHref.includes('size=')) {
                        actionText = '正在调整页面大小...';
                    }
                    
                    overlayManager.show(actionText);
                    
                    // 添加安全机制，防止请求失败时遮罩层卡死
                    setTimeout(() => {
                        overlayManager.hide();
                    }, 8000); // 8秒后自动隐藏遮罩层
                }
            }, 500));
            
            link.dataset.debounceApplied = 'true';
        }
    });
    
    // 为模态框中的按钮添加防抖
    const modalButtons = document.querySelectorAll('[data-bs-toggle="modal"]');
    modalButtons.forEach(button => {
        button.addEventListener('click', debounce(function(e) {
            // 不需要显示遮罩层，因为模态框本身就有遮罩
        }, 500));
    });
});
