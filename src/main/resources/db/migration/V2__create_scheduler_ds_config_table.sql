-- 创建调度器数据源配置表
CREATE TABLE t_pr_scheduler_ds_config (
    id VARCHAR2(100) PRIMARY KEY,
    data_source_type VARCHAR2(20) NOT NULL,
    data_source_host VARCHAR2(200) NOT NULL,
    data_source_port VARCHAR2(10) NOT NULL,
    data_source_instance VARCHAR2(100) NOT NULL,
    data_source_user VARCHAR2(100) NOT NULL,
    data_source_password VARCHAR2(200) NOT NULL,
    description VARCHAR2(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- 创建索引
CREATE INDEX idx_scheduler_ds_config_type ON t_pr_scheduler_ds_config(data_source_type);
CREATE INDEX idx_scheduler_ds_config_created ON t_pr_scheduler_ds_config(created_at);

-- 添加注释
COMMENT ON TABLE t_pr_scheduler_ds_config IS '调度器数据源配置表';
COMMENT ON COLUMN t_pr_scheduler_ds_config.id IS '数据源ID';
COMMENT ON COLUMN t_pr_scheduler_ds_config.data_source_type IS '数据源类型(ORACLE/MYSQL/SQL_SERVER)';
COMMENT ON COLUMN t_pr_scheduler_ds_config.data_source_host IS '数据库主机地址';
COMMENT ON COLUMN t_pr_scheduler_ds_config.data_source_port IS '数据库端口';
COMMENT ON COLUMN t_pr_scheduler_ds_config.data_source_instance IS '数据库实例名';
COMMENT ON COLUMN t_pr_scheduler_ds_config.data_source_user IS '数据库用户名';
COMMENT ON COLUMN t_pr_scheduler_ds_config.data_source_password IS '数据库密码';
COMMENT ON COLUMN t_pr_scheduler_ds_config.description IS '描述信息';
COMMENT ON COLUMN t_pr_scheduler_ds_config.created_at IS '创建时间';
COMMENT ON COLUMN t_pr_scheduler_ds_config.updated_at IS '更新时间';
