package com.vincenttho.pr.scheduler.service

import com.vincenttho.pr.scheduler.entity.DataSourceTypeEnum
import com.vincenttho.pr.scheduler.entity.SchedulerDsConfig
import com.vincenttho.pr.scheduler.repository.SchedulerDsConfigRepository
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mock
import org.mockito.Mockito.*
import org.mockito.MockitoAnnotations
import org.springframework.jdbc.core.JdbcTemplate
import java.util.*

/**
 * DynamicDataSourceService 测试类
 * <AUTHOR>
 */
class DynamicDataSourceServiceTest {
    
    @Mock
    private lateinit var schedulerDsConfigRepository: SchedulerDsConfigRepository
    
    @Mock
    private lateinit var primaryJdbcTemplate: JdbcTemplate
    
    private lateinit var dynamicDataSourceService: DynamicDataSourceService
    
    @BeforeEach
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        dynamicDataSourceService = DynamicDataSourceService(schedulerDsConfigRepository, primaryJdbcTemplate)
    }
    
    @Test
    fun `should return primary jdbc template when dataSourceId is null`() {
        // When
        val result = dynamicDataSourceService.getPrimaryJdbcTemplate()
        
        // Then
        assert(result == primaryJdbcTemplate)
    }
    
    @Test
    fun `should return null when datasource config not found`() {
        // Given
        val dataSourceId = "non-existent"
        `when`(schedulerDsConfigRepository.findById(dataSourceId)).thenReturn(Optional.empty())
        
        // When
        val result = dynamicDataSourceService.getJdbcTemplate(dataSourceId)
        
        // Then
        assert(result == null)
    }
    
    @Test
    fun `should cache datasource after first creation`() {
        // Given
        val dataSourceId = "test-ds"
        val dsConfig = SchedulerDsConfig(
            id = dataSourceId,
            dataSourceType = DataSourceTypeEnum.ORACLE,
            dataSourceHost = "localhost",
            dataSourcePort = "1521",
            dataSourceInstance = "XE",
            dataSourceUser = "test",
            dataSourcePassword = "test"
        )
        
        `when`(schedulerDsConfigRepository.findById(dataSourceId)).thenReturn(Optional.of(dsConfig))
        
        // When
        val isCachedBefore = dynamicDataSourceService.isCached(dataSourceId)
        
        // Then
        assert(!isCachedBefore)
        
        // Note: 实际的数据源创建测试需要真实的数据库连接，这里只测试缓存逻辑
    }
    
    @Test
    fun `should clear cache when requested`() {
        // Given
        val dataSourceId = "test-ds"
        
        // When
        dynamicDataSourceService.clearCache(dataSourceId)
        
        // Then
        val isCached = dynamicDataSourceService.isCached(dataSourceId)
        assert(!isCached)
    }
}
