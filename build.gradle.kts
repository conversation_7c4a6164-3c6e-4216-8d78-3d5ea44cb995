plugins {
    java
    kotlin("jvm") version "2.1.0"
    kotlin("plugin.spring") version "2.1.0"
    kotlin("kapt") version "2.1.0"
    id("org.springframework.boot") version "3.4.1"
    id("io.spring.dependency-management") version "1.1.7"
    id("maven-publish")
}

group = "com.vincenttho"
version = "0.0.1-SNAPSHOT"
// Artifact ID is defined by the directory name and rootProject.name in settings.gradle.kts

java {
    withJavadocJar()
    withSourcesJar()
}

repositories {
    maven(url = "https://maven.aliyun.com/repository/public/")
    maven(url = "https://maven.aliyun.com/repository/spring/")
    mavenCentral()
}

dependencies {
    api("org.springframework.boot:spring-boot-starter")
    api("org.springframework.boot:spring-boot-starter-web")
    api("org.springframework.boot:spring-boot-starter-thymeleaf")
    api("org.springframework.boot:spring-boot-starter-data-jpa")
    api("com.fasterxml.jackson.module:jackson-module-kotlin")
    api("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")
    api("org.jetbrains.kotlin:kotlin-reflect")
    implementation("jakarta.annotation:jakarta.annotation-api")

    // Make database drivers optional
    implementation("com.oracle.database.jdbc:ojdbc8:19.12.0.0")
    implementation("com.oracle.ojdbc:orai18n:19.3.0.0")
    implementation("com.microsoft.sqlserver:mssql-jdbc:7.3.0.jre8-preview")
    implementation("mysql:mysql-connector-java:5.1.47")

    annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")



    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.jetbrains.kotlin:kotlin-test-junit5")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
}

kotlin {
    jvmToolchain(21)
}