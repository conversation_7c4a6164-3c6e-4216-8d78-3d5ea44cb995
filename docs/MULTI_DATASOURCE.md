# 多数据源支持文档

## 概述

本系统支持多数据源功能，允许调度器任务在不同的数据库上执行查询。系统会根据调度器配置中的 `dataSourceId` 字段来决定使用哪个数据源。

## 功能特性

- **动态数据源切换**: 根据调度器配置自动切换到指定的数据源
- **数据源缓存**: 已创建的数据源会被缓存，提高性能
- **连接测试**: 支持测试数据源连接是否正常
- **回退机制**: 当指定数据源不可用时，自动回退到主数据源
- **支持多种数据库**: Oracle、MySQL、SQL Server

## 数据源配置

### 1. 创建数据源配置

数据源配置通过 `SchedulerDsConfig` 实体进行管理：

```kotlin
val dsConfig = SchedulerDsConfig(
    id = "oracle-test",
    dataSourceType = DataSourceTypeEnum.ORACLE,
    dataSourceHost = "*************",
    dataSourcePort = "1521",
    dataSourceInstance = "XE",
    dataSourceUser = "test_user",
    dataSourcePassword = "test_password",
    description = "测试Oracle数据源"
)
```

### 2. 支持的数据库类型

- **Oracle**: `DataSourceTypeEnum.ORACLE`
- **MySQL**: `DataSourceTypeEnum.MYSQL`
- **SQL Server**: `DataSourceTypeEnum.SQL_SERVER`

## 使用方式

### 1. 配置调度器使用特定数据源

在创建或更新调度器时，设置 `dataSourceId` 字段：

```kotlin
val scheduler = SchedulerConfig(
    id = "patient-reminder",
    name = "患者提醒",
    cronExpression = "0 */5 * * * ?",
    sql = "SELECT message FROM patient_reminders WHERE created_time >= ?",
    dataSourceId = "oracle-test"  // 指定使用的数据源ID
)
```

### 2. 使用主数据源

如果不设置 `dataSourceId` 或设置为 `null`，系统将使用主数据源：

```kotlin
val scheduler = SchedulerConfig(
    id = "patient-reminder",
    name = "患者提醒",
    cronExpression = "0 */5 * * * ?",
    sql = "SELECT message FROM patient_reminders WHERE created_time >= ?",
    dataSourceId = null  // 使用主数据源
)
```

## API 接口

### 数据源管理接口

- `GET /schedulers/datasources` - 获取数据源配置列表
- `POST /schedulers/datasources` - 创建数据源配置
- `GET /schedulers/datasources/{id}` - 获取指定数据源配置
- `PUT /schedulers/datasources/{id}` - 更新数据源配置
- `DELETE /schedulers/datasources/{id}` - 删除数据源配置
- `POST /schedulers/datasources/{id}/test` - 测试数据源连接

### 获取数据源选项

```http
GET /schedulers/datasources/options
```

返回格式：
```json
[
    {
        "id": "oracle-test",
        "name": "oracle-test (ORACLE)"
    },
    {
        "id": "mysql-prod",
        "name": "mysql-prod (MYSQL)"
    }
]
```

## 工作原理

### 1. 数据源选择逻辑

```kotlin
val targetJdbcTemplate = if (scheduler.dataSourceId.isNullOrBlank()) {
    // 使用主数据源
    logger.debug("使用主数据源执行查询")
    dynamicDataSourceService.getPrimaryJdbcTemplate()
} else {
    // 使用指定的数据源
    logger.info("尝试使用数据源: {}", scheduler.dataSourceId)
    val customJdbcTemplate = dynamicDataSourceService.getJdbcTemplate(scheduler.dataSourceId!!)
    if (customJdbcTemplate != null) {
        logger.info("成功切换到数据源: {}", scheduler.dataSourceId)
        customJdbcTemplate
    } else {
        logger.warn("无法获取数据源: {}，回退到主数据源", scheduler.dataSourceId)
        dynamicDataSourceService.getPrimaryJdbcTemplate()
    }
}
```

### 2. 数据源缓存机制

- 首次使用数据源时，系统会根据配置创建 `DataSource` 和 `JdbcTemplate`
- 创建成功后，数据源会被缓存在内存中
- 后续使用相同数据源时，直接从缓存中获取
- 当数据源配置更新时，相关缓存会被清除

### 3. 错误处理

- 如果指定的数据源配置不存在，系统会记录警告并使用主数据源
- 如果数据源连接失败，系统会记录错误并使用主数据源
- 所有数据源操作都有详细的日志记录

## 注意事项

1. **密码安全**: 数据源密码以明文存储在数据库中，生产环境建议使用加密存储
2. **连接池**: 当前实现使用 `DriverManagerDataSource`，生产环境建议使用连接池
3. **事务管理**: 多数据源环境下的事务管理需要特别注意
4. **性能考虑**: 数据源缓存可以提高性能，但也会占用内存

## 示例

### 完整的多数据源配置示例

```kotlin
// 1. 创建Oracle数据源配置
val oracleConfig = SchedulerDsConfig(
    id = "oracle-prod",
    dataSourceType = DataSourceTypeEnum.ORACLE,
    dataSourceHost = "prod-oracle.company.com",
    dataSourcePort = "1521",
    dataSourceInstance = "PROD",
    dataSourceUser = "scheduler_user",
    dataSourcePassword = "secure_password",
    description = "生产环境Oracle数据库"
)

// 2. 创建MySQL数据源配置
val mysqlConfig = SchedulerDsConfig(
    id = "mysql-analytics",
    dataSourceType = DataSourceTypeEnum.MYSQL,
    dataSourceHost = "analytics-mysql.company.com",
    dataSourcePort = "3306",
    dataSourceInstance = "analytics",
    dataSourceUser = "analytics_user",
    dataSourcePassword = "analytics_password",
    description = "分析数据库MySQL"
)

// 3. 创建使用Oracle数据源的调度器
val oracleScheduler = SchedulerConfig(
    id = "oracle-patient-reminder",
    name = "Oracle患者提醒",
    cronExpression = "0 */10 * * * ?",
    sql = "SELECT message FROM patient_reminders WHERE created_time >= ?",
    dataSourceId = "oracle-prod"
)

// 4. 创建使用MySQL数据源的调度器
val mysqlScheduler = SchedulerConfig(
    id = "mysql-analytics-report",
    name = "MySQL分析报告",
    cronExpression = "0 0 8 * * ?",
    sql = "SELECT report_content FROM daily_reports WHERE report_date >= ?",
    dataSourceId = "mysql-analytics"
)
```

## 故障排除

### 常见问题

1. **数据源连接失败**
   - 检查网络连接
   - 验证数据库服务是否运行
   - 确认用户名密码正确
   - 检查数据库权限

2. **找不到数据源配置**
   - 确认数据源ID是否正确
   - 检查数据源配置是否已保存

3. **SQL执行失败**
   - 确认SQL语法正确
   - 检查表和字段是否存在
   - 验证数据库权限

### 日志查看

系统会记录详细的多数据源操作日志：

```
INFO  - 尝试使用数据源: oracle-prod
INFO  - 成功切换到数据源: oracle-prod
WARN  - 无法获取数据源: invalid-ds，回退到主数据源
ERROR - 创建数据源失败: mysql-test, 错误: Connection refused
```
